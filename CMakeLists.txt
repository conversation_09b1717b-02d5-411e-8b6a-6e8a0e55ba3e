cmake_minimum_required(VERSION 3.16)
project(IDMDownloader VERSION 1.0.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)
find_package(Threads REQUIRED)

# Find libcurl
find_package(CURL REQUIRED)

# Include directories
include_directories(include)

# Source files
set(SOURCES
    src/main.cpp
    src/downloader.cpp
    src/http_client.cpp
    src/progress_tracker.cpp
    src/file_manager.cpp
    src/utils.cpp
)

# Header files
set(HEADERS
    include/downloader.h
    include/http_client.h
    include/progress_tracker.h
    include/file_manager.h
    include/utils.h
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES} ${HEADERS})

# Link libraries
target_link_libraries(${PROJECT_NAME} 
    PRIVATE 
    ${CURL_LIBRARIES}
    Threads::Threads
)

# Include directories for curl
target_include_directories(${PROJECT_NAME} PRIVATE ${CURL_INCLUDE_DIRS})

# Compiler flags
target_compile_options(${PROJECT_NAME} PRIVATE 
    -Wall 
    -Wextra 
    -O2
)

# Install target
install(TARGETS ${PROJECT_NAME} DESTINATION bin)
