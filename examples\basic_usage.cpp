#include "../include/downloader.h"
#include "../include/utils.h"
#include <iostream>
#include <thread>
#include <chrono>

// Example demonstrating basic usage of the IDM Downloader library

void example_basic_download() {
    std::cout << "=== Basic Download Example ===\n";
    
    Downloader downloader;
    
    DownloadConfig config;
    config.url = "https://httpbin.org/bytes/1048576"; // 1MB test file
    config.output_path = "test_download.bin";
    config.max_connections = 1;
    config.show_progress = true;
    
    std::cout << "Downloading 1MB test file...\n";
    
    bool success = downloader.download(config);
    
    if (success) {
        std::cout << "Download completed successfully!\n";
        std::cout << "File saved to: " << config.output_path << "\n";
        
        // Verify file size
        size_t file_size = Utils::getFileSize(config.output_path);
        std::cout << "Downloaded file size: " << Utils::formatBytes(file_size) << "\n";
    } else {
        std::cout << "Download failed!\n";
        auto stats = downloader.getStats();
        if (stats.has_error) {
            std::cout << "Error: " << stats.error_message << "\n";
        }
    }
    
    std::cout << "\n";
}

void example_multithreaded_download() {
    std::cout << "=== Multi-threaded Download Example ===\n";
    
    Downloader downloader;
    
    DownloadConfig config;
    config.url = "https://httpbin.org/bytes/10485760"; // 10MB test file
    config.output_path = "test_download_mt.bin";
    config.max_connections = 4;
    config.show_progress = true;
    config.chunk_size = 1024 * 1024; // 1MB chunks
    
    std::cout << "Downloading 10MB test file with 4 connections...\n";
    
    bool success = downloader.download(config);
    
    if (success) {
        std::cout << "Multi-threaded download completed successfully!\n";
        std::cout << "File saved to: " << config.output_path << "\n";
        
        // Verify file size
        size_t file_size = Utils::getFileSize(config.output_path);
        std::cout << "Downloaded file size: " << Utils::formatBytes(file_size) << "\n";
    } else {
        std::cout << "Multi-threaded download failed!\n";
        auto stats = downloader.getStats();
        if (stats.has_error) {
            std::cout << "Error: " << stats.error_message << "\n";
        }
    }
    
    std::cout << "\n";
}

void example_async_download_with_callbacks() {
    std::cout << "=== Async Download with Callbacks Example ===\n";
    
    Downloader downloader;
    
    DownloadConfig config;
    config.url = "https://httpbin.org/bytes/5242880"; // 5MB test file
    config.output_path = "test_download_async.bin";
    config.max_connections = 2;
    config.show_progress = false; // We'll handle progress ourselves
    
    std::cout << "Starting async download of 5MB test file...\n";
    
    bool download_completed = false;
    bool download_success = false;
    std::string error_message;
    
    // Progress callback
    auto progress_callback = [](const DownloadStats& stats) {
        std::cout << "\rProgress: " << Utils::formatPercentage(stats.progress_percentage)
                  << " (" << Utils::formatBytes(stats.downloaded_size) 
                  << "/" << Utils::formatBytes(stats.total_size) << ") "
                  << Utils::formatSpeed(stats.download_speed);
        if (!stats.eta.empty()) {
            std::cout << " ETA: " << stats.eta;
        }
        std::cout.flush();
    };
    
    // Completion callback
    auto completion_callback = [&](bool success, const std::string& error) {
        download_completed = true;
        download_success = success;
        error_message = error;
    };
    
    // Start async download
    downloader.downloadAsync(config, progress_callback, completion_callback);
    
    // Wait for completion while showing that we can do other work
    int dots = 0;
    while (!download_completed) {
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        // Simulate doing other work
        if (dots % 10 == 0) {
            std::cout << "\n[Main thread is free to do other work...]";
        }
        dots++;
    }
    
    std::cout << "\n";
    
    if (download_success) {
        std::cout << "Async download completed successfully!\n";
        std::cout << "File saved to: " << config.output_path << "\n";
        
        // Verify file size
        size_t file_size = Utils::getFileSize(config.output_path);
        std::cout << "Downloaded file size: " << Utils::formatBytes(file_size) << "\n";
    } else {
        std::cout << "Async download failed!\n";
        if (!error_message.empty()) {
            std::cout << "Error: " << error_message << "\n";
        }
    }
    
    std::cout << "\n";
}

void example_resume_download() {
    std::cout << "=== Resume Download Example ===\n";
    
    Downloader downloader;
    
    DownloadConfig config;
    config.url = "https://httpbin.org/bytes/3145728"; // 3MB test file
    config.output_path = "test_download_resume.bin";
    config.max_connections = 1;
    config.resume_download = true;
    config.show_progress = true;
    
    std::cout << "First download attempt (will be interrupted)...\n";
    
    // Start download in a separate thread so we can interrupt it
    std::thread download_thread([&]() {
        downloader.download(config);
    });
    
    // Let it download for a bit, then cancel
    std::this_thread::sleep_for(std::chrono::seconds(2));
    std::cout << "\nInterrupting download...\n";
    downloader.cancel();
    
    if (download_thread.joinable()) {
        download_thread.join();
    }
    
    // Check if partial file exists
    if (Utils::fileExists(config.output_path)) {
        size_t partial_size = Utils::getFileSize(config.output_path);
        std::cout << "Partial file size: " << Utils::formatBytes(partial_size) << "\n";
        
        // Now resume the download
        std::cout << "Resuming download...\n";
        
        Downloader resume_downloader;
        bool success = resume_downloader.download(config);
        
        if (success) {
            std::cout << "Resume download completed successfully!\n";
            std::cout << "File saved to: " << config.output_path << "\n";
            
            // Verify final file size
            size_t final_size = Utils::getFileSize(config.output_path);
            std::cout << "Final file size: " << Utils::formatBytes(final_size) << "\n";
        } else {
            std::cout << "Resume download failed!\n";
        }
    } else {
        std::cout << "No partial file found to resume.\n";
    }
    
    std::cout << "\n";
}

void cleanup_test_files() {
    std::cout << "Cleaning up test files...\n";
    
    std::vector<std::string> test_files = {
        "test_download.bin",
        "test_download_mt.bin", 
        "test_download_async.bin",
        "test_download_resume.bin",
        "test_download.bin.idm_resume",
        "test_download_mt.bin.idm_resume",
        "test_download_async.bin.idm_resume", 
        "test_download_resume.bin.idm_resume"
    };
    
    for (const auto& file : test_files) {
        if (Utils::fileExists(file)) {
            try {
                std::filesystem::remove(file);
                std::cout << "Removed: " << file << "\n";
            } catch (const std::exception& e) {
                std::cout << "Failed to remove " << file << ": " << e.what() << "\n";
            }
        }
    }
}

int main() {
    std::cout << "IDM Downloader - Usage Examples\n";
    std::cout << "================================\n\n";
    
    try {
        // Run examples
        example_basic_download();
        example_multithreaded_download();
        example_async_download_with_callbacks();
        example_resume_download();
        
        // Cleanup
        cleanup_test_files();
        
        std::cout << "All examples completed!\n";
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << "\n";
        return 1;
    }
    
    return 0;
}
