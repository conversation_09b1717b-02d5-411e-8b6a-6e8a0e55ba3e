#include "http_client.h"
#include <curl/curl.h>
#include <sstream>
#include <iostream>
#include <algorithm>

// Callback functions for libcurl
static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    size_t realsize = size * nmemb;
    auto* response = static_cast<HttpResponse*>(userp);
    
    size_t old_size = response->body.size();
    response->body.resize(old_size + realsize);
    std::memcpy(response->body.data() + old_size, contents, realsize);
    
    return realsize;
}

static size_t WriteFileCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    size_t realsize = size * nmemb;
    auto* write_cb = static_cast<HttpClient::WriteCallback*>(userp);
    
    if (write_cb) {
        return (*write_cb)(static_cast<const char*>(contents), realsize);
    }
    
    return realsize;
}

static size_t HeaderCallback(void* contents, size_t size, size_t nmemb, void* userp) {
    size_t realsize = size * nmemb;
    auto* response = static_cast<HttpResponse*>(userp);
    
    std::string header(static_cast<char*>(contents), realsize);
    
    // Parse header
    size_t colon_pos = header.find(':');
    if (colon_pos != std::string::npos) {
        std::string key = header.substr(0, colon_pos);
        std::string value = header.substr(colon_pos + 1);
        
        // Trim whitespace
        key.erase(0, key.find_first_not_of(" \t\r\n"));
        key.erase(key.find_last_not_of(" \t\r\n") + 1);
        value.erase(0, value.find_first_not_of(" \t\r\n"));
        value.erase(value.find_last_not_of(" \t\r\n") + 1);
        
        // Convert key to lowercase for case-insensitive lookup
        std::transform(key.begin(), key.end(), key.begin(), ::tolower);
        
        response->headers[key] = value;
    }
    
    return realsize;
}

static int ProgressCallback(void* clientp, curl_off_t dltotal, curl_off_t dlnow, 
                           curl_off_t ultotal, curl_off_t ulnow) {
    auto* progress_cb = static_cast<HttpClient::ProgressCallback*>(clientp);
    
    if (progress_cb && dltotal > 0) {
        bool should_continue = (*progress_cb)(static_cast<size_t>(dlnow), 
                                            static_cast<size_t>(dltotal));
        return should_continue ? 0 : 1; // Return 1 to abort
    }
    
    return 0;
}

class HttpClient::Impl {
public:
    Impl() {
        curl_global_init(CURL_GLOBAL_DEFAULT);
        curl_handle_ = curl_easy_init();
        
        if (curl_handle_) {
            // Set default options
            curl_easy_setopt(curl_handle_, CURLOPT_FOLLOWLOCATION, 1L);
            curl_easy_setopt(curl_handle_, CURLOPT_MAXREDIRS, 10L);
            curl_easy_setopt(curl_handle_, CURLOPT_TIMEOUT, 30L);
            curl_easy_setopt(curl_handle_, CURLOPT_USERAGENT, "IDM-Downloader/1.0");
            curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYPEER, 1L);
            curl_easy_setopt(curl_handle_, CURLOPT_SSL_VERIFYHOST, 2L);
        }
    }
    
    ~Impl() {
        if (curl_handle_) {
            curl_easy_cleanup(curl_handle_);
        }
        curl_global_cleanup();
    }
    
    CURL* curl_handle_ = nullptr;
    int timeout_seconds_ = 30;
    std::string user_agent_ = "IDM-Downloader/1.0";
    bool follow_redirects_ = true;
    int max_redirects_ = 10;
};

HttpClient::HttpClient() : pimpl_(std::make_unique<Impl>()) {}

HttpClient::~HttpClient() = default;

HttpResponse HttpClient::get(const std::string& url, 
                           const std::map<std::string, std::string>& headers) {
    HttpResponse response;
    
    if (!pimpl_->curl_handle_) {
        response.error_message = "Failed to initialize curl";
        return response;
    }
    
    // Set URL
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_URL, url.c_str());
    
    // Set callbacks
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HEADERFUNCTION, HeaderCallback);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HEADERDATA, &response);
    
    // Set headers
    struct curl_slist* header_list = nullptr;
    for (const auto& header : headers) {
        std::string header_str = header.first + ": " + header.second;
        header_list = curl_slist_append(header_list, header_str.c_str());
    }
    
    if (header_list) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HTTPHEADER, header_list);
    }
    
    // Perform request
    CURLcode res = curl_easy_perform(pimpl_->curl_handle_);
    
    if (header_list) {
        curl_slist_free_all(header_list);
    }
    
    if (res == CURLE_OK) {
        curl_easy_getinfo(pimpl_->curl_handle_, CURLINFO_RESPONSE_CODE, &response.status_code);
        response.success = (response.status_code >= 200 && response.status_code < 300);
    } else {
        response.error_message = curl_easy_strerror(res);
    }
    
    return response;
}

HttpResponse HttpClient::head(const std::string& url,
                            const std::map<std::string, std::string>& headers) {
    HttpResponse response;
    
    if (!pimpl_->curl_handle_) {
        response.error_message = "Failed to initialize curl";
        return response;
    }
    
    // Set URL and HEAD method
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_URL, url.c_str());
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_NOBODY, 1L);
    
    // Set callbacks
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HEADERFUNCTION, HeaderCallback);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HEADERDATA, &response);
    
    // Set headers
    struct curl_slist* header_list = nullptr;
    for (const auto& header : headers) {
        std::string header_str = header.first + ": " + header.second;
        header_list = curl_slist_append(header_list, header_str.c_str());
    }
    
    if (header_list) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HTTPHEADER, header_list);
    }
    
    // Perform request
    CURLcode res = curl_easy_perform(pimpl_->curl_handle_);
    
    // Reset NOBODY option
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_NOBODY, 0L);
    
    if (header_list) {
        curl_slist_free_all(header_list);
    }
    
    if (res == CURLE_OK) {
        curl_easy_getinfo(pimpl_->curl_handle_, CURLINFO_RESPONSE_CODE, &response.status_code);
        response.success = (response.status_code >= 200 && response.status_code < 300);
    } else {
        response.error_message = curl_easy_strerror(res);
    }
    
    return response;
}

bool HttpClient::download(const HttpRequest& request,
                         WriteCallback write_cb,
                         ProgressCallback progress_cb) {
    if (!pimpl_->curl_handle_) {
        return false;
    }
    
    // Set URL
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_URL, request.url.c_str());
    
    // Set write callback
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_WRITEFUNCTION, WriteFileCallback);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_WRITEDATA, &write_cb);
    
    // Set progress callback
    if (progress_cb) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_XFERINFOFUNCTION, ProgressCallback);
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_XFERINFODATA, &progress_cb);
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_NOPROGRESS, 0L);
    }
    
    // Set range if specified
    if (request.use_range) {
        std::string range = std::to_string(request.range_start) + "-";
        if (request.range_end > 0) {
            range += std::to_string(request.range_end);
        }
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_RANGE, range.c_str());
    }
    
    // Set headers
    struct curl_slist* header_list = nullptr;
    for (const auto& header : request.headers) {
        std::string header_str = header.first + ": " + header.second;
        header_list = curl_slist_append(header_list, header_str.c_str());
    }
    
    if (header_list) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_HTTPHEADER, header_list);
    }
    
    // Perform request
    CURLcode res = curl_easy_perform(pimpl_->curl_handle_);
    
    // Cleanup
    if (header_list) {
        curl_slist_free_all(header_list);
    }
    
    // Reset range option
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_RANGE, nullptr);
    curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_NOPROGRESS, 1L);
    
    return (res == CURLE_OK);
}

bool HttpClient::downloadRange(const std::string& url,
                              size_t start,
                              size_t end,
                              WriteCallback write_cb,
                              ProgressCallback progress_cb) {
    HttpRequest request;
    request.url = url;
    request.use_range = true;
    request.range_start = start;
    request.range_end = end;

    return download(request, write_cb, progress_cb);
}

bool HttpClient::supportsRangeRequests(const std::string& url) {
    auto response = head(url);

    if (!response.success) {
        return false;
    }

    // Check for Accept-Ranges header
    auto it = response.headers.find("accept-ranges");
    if (it != response.headers.end()) {
        return (it->second == "bytes");
    }

    return false;
}

size_t HttpClient::getContentLength(const std::string& url) {
    auto response = head(url);

    if (!response.success) {
        return 0;
    }

    auto it = response.headers.find("content-length");
    if (it != response.headers.end()) {
        try {
            return std::stoull(it->second);
        } catch (const std::exception&) {
            return 0;
        }
    }

    return 0;
}

std::string HttpClient::getContentType(const std::string& url) {
    auto response = head(url);

    if (!response.success) {
        return "";
    }

    auto it = response.headers.find("content-type");
    if (it != response.headers.end()) {
        return it->second;
    }

    return "";
}

void HttpClient::setTimeout(int seconds) {
    pimpl_->timeout_seconds_ = seconds;
    if (pimpl_->curl_handle_) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_TIMEOUT, static_cast<long>(seconds));
    }
}

void HttpClient::setUserAgent(const std::string& user_agent) {
    pimpl_->user_agent_ = user_agent;
    if (pimpl_->curl_handle_) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_USERAGENT, user_agent.c_str());
    }
}

void HttpClient::setFollowRedirects(bool follow) {
    pimpl_->follow_redirects_ = follow;
    if (pimpl_->curl_handle_) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_FOLLOWLOCATION, follow ? 1L : 0L);
    }
}

void HttpClient::setMaxRedirects(int max_redirects) {
    pimpl_->max_redirects_ = max_redirects;
    if (pimpl_->curl_handle_) {
        curl_easy_setopt(pimpl_->curl_handle_, CURLOPT_MAXREDIRS, static_cast<long>(max_redirects));
    }
}
