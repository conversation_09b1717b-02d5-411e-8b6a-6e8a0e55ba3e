#include "main_window.h"
#include "../include/downloader.h"
#include <QtWidgets/QApplication>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QHeaderView>
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtCore/QUrl>
#include <QtGui/QIcon>
#include <QtGui/QPixmap>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_downloadThread(nullptr)
    , m_downloadWorker(nullptr)
    , m_isDownloading(false)
{
    setWindowTitle("IDM Downloader - Internet Download Manager");
    setWindowIcon(QIcon(":/icons/idm_icon.png"));
    resize(800, 600);
    setMinimumSize(600, 400);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    connectSignals();
    applyIDMStyle();
    
    // Initialize update timer
    m_updateTimer = new QTimer(this);
    connect(m_updateTimer, &QTimer::timeout, this, &MainWindow::updateDownloadList);
    
    // Set default download path
    QString defaultPath = QStandardPaths::writableLocation(QStandardPaths::DownloadLocation);
    m_outputPathEdit->setText(defaultPath);
    
    // Initialize backend
    m_downloadThread = new QThread(this);
    m_downloadWorker = new DownloadWorker();
    m_downloadWorker->moveToThread(m_downloadThread);
    
    connect(m_downloadWorker, &DownloadWorker::progressUpdated,
            this, &MainWindow::updateProgress);
    connect(m_downloadWorker, &DownloadWorker::downloadFinished,
            this, &MainWindow::downloadFinished);
    
    m_downloadThread->start();
}

MainWindow::~MainWindow()
{
    if (m_downloadThread) {
        m_downloadThread->quit();
        m_downloadThread->wait();
    }
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Main layout
    auto* mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Vertical, this);
    mainLayout->addWidget(m_mainSplitter);
    
    setupMainArea();
    
    // Status area at bottom
    auto* statusWidget = new QWidget();
    auto* statusLayout = new QHBoxLayout(statusWidget);
    
    m_statusLabel = new QLabel("Ready");
    m_speedLabel = new QLabel("Speed: 0 KB/s");
    m_etaLabel = new QLabel("ETA: --:--");
    m_fileSizeLabel = new QLabel("Size: Unknown");
    
    statusLayout->addWidget(m_statusLabel);
    statusLayout->addStretch();
    statusLayout->addWidget(m_speedLabel);
    statusLayout->addWidget(m_etaLabel);
    statusLayout->addWidget(m_fileSizeLabel);
    
    mainLayout->addWidget(statusWidget);
}

void MainWindow::setupMainArea()
{
    // Create tab widget
    m_tabWidget = new QTabWidget();
    m_mainSplitter->addWidget(m_tabWidget);
    
    setupDownloadArea();
    setupQueueArea();
    setupLogArea();
}

void MainWindow::setupDownloadArea()
{
    m_downloadWidget = new QWidget();
    m_tabWidget->addTab(m_downloadWidget, "Download");
    
    auto* layout = new QVBoxLayout(m_downloadWidget);
    
    // URL input group
    auto* urlGroup = new QGroupBox("Download URL");
    auto* urlLayout = new QVBoxLayout(urlGroup);
    
    m_urlEdit = new QLineEdit();
    m_urlEdit->setPlaceholderText("Enter URL to download...");
    urlLayout->addWidget(m_urlEdit);
    
    layout->addWidget(urlGroup);
    
    // Output path group
    auto* pathGroup = new QGroupBox("Save to");
    auto* pathLayout = new QHBoxLayout(pathGroup);
    
    m_outputPathEdit = new QLineEdit();
    m_browseButton = new QPushButton("Browse...");
    m_browseButton->setMaximumWidth(80);
    
    pathLayout->addWidget(m_outputPathEdit);
    pathLayout->addWidget(m_browseButton);
    
    layout->addWidget(pathGroup);
    
    // Options group
    auto* optionsGroup = new QGroupBox("Download Options");
    auto* optionsLayout = new QGridLayout(optionsGroup);
    
    optionsLayout->addWidget(new QLabel("Connections:"), 0, 0);
    m_connectionsSpinBox = new QSpinBox();
    m_connectionsSpinBox->setRange(1, 16);
    m_connectionsSpinBox->setValue(4);
    optionsLayout->addWidget(m_connectionsSpinBox, 0, 1);
    
    optionsLayout->addWidget(new QLabel("Category:"), 0, 2);
    m_categoryComboBox = new QComboBox();
    m_categoryComboBox->addItems({"General", "Documents", "Music", "Videos", "Software", "Archives"});
    optionsLayout->addWidget(m_categoryComboBox, 0, 3);
    
    m_resumeCheckBox = new QCheckBox("Resume download");
    m_resumeCheckBox->setChecked(true);
    optionsLayout->addWidget(m_resumeCheckBox, 1, 0, 1, 2);
    
    m_shutdownCheckBox = new QCheckBox("Shutdown when complete");
    optionsLayout->addWidget(m_shutdownCheckBox, 1, 2, 1, 2);
    
    layout->addWidget(optionsGroup);
    
    // Progress group
    auto* progressGroup = new QGroupBox("Progress");
    auto* progressLayout = new QVBoxLayout(progressGroup);
    
    m_progressBar = new QProgressBar();
    m_progressBar->setTextVisible(true);
    progressLayout->addWidget(m_progressBar);
    
    layout->addWidget(progressGroup);
    
    // Control buttons
    auto* buttonLayout = new QHBoxLayout();
    
    m_startButton = new QPushButton("Start Download");
    m_pauseButton = new QPushButton("Pause");
    m_resumeButton = new QPushButton("Resume");
    m_stopButton = new QPushButton("Stop");
    m_addToQueueButton = new QPushButton("Add to Queue");
    
    m_startButton->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
    m_pauseButton->setStyleSheet("QPushButton { background-color: #FF9800; color: white; }");
    m_resumeButton->setStyleSheet("QPushButton { background-color: #2196F3; color: white; }");
    m_stopButton->setStyleSheet("QPushButton { background-color: #F44336; color: white; }");
    
    m_pauseButton->setEnabled(false);
    m_resumeButton->setEnabled(false);
    m_stopButton->setEnabled(false);
    
    buttonLayout->addWidget(m_startButton);
    buttonLayout->addWidget(m_pauseButton);
    buttonLayout->addWidget(m_resumeButton);
    buttonLayout->addWidget(m_stopButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_addToQueueButton);
    
    layout->addLayout(buttonLayout);
    layout->addStretch();
}

void MainWindow::setupQueueArea()
{
    auto* queueWidget = new QWidget();
    m_tabWidget->addTab(queueWidget, "Queue");

    auto* layout = new QVBoxLayout(queueWidget);

    // Queue table
    m_queueTable = new QTableWidget(0, 6);
    QStringList headers = {"URL", "File Name", "Size", "Progress", "Speed", "Status"};
    m_queueTable->setHorizontalHeaderLabels(headers);
    m_queueTable->horizontalHeader()->setStretchLastSection(true);
    m_queueTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_queueTable->setAlternatingRowColors(true);

    layout->addWidget(m_queueTable);

    // Queue control buttons
    auto* queueButtonLayout = new QHBoxLayout();
    m_startQueueButton = new QPushButton("Start Queue");
    m_clearQueueButton = new QPushButton("Clear Queue");

    queueButtonLayout->addWidget(m_startQueueButton);
    queueButtonLayout->addWidget(m_clearQueueButton);
    queueButtonLayout->addStretch();

    layout->addLayout(queueButtonLayout);
}

void MainWindow::setupLogArea()
{
    auto* logWidget = new QWidget();
    m_tabWidget->addTab(logWidget, "Log");

    auto* layout = new QVBoxLayout(logWidget);

    m_logTextEdit = new QTextEdit();
    m_logTextEdit->setReadOnly(true);
    m_logTextEdit->setFont(QFont("Consolas", 9));

    layout->addWidget(m_logTextEdit);

    // Add initial log message
    m_logTextEdit->append(QString("[%1] IDM Downloader started")
                         .arg(QDateTime::currentDateTime().toString()));
}

void MainWindow::setupMenuBar()
{
    m_menuBar = menuBar();

    // File menu
    auto* fileMenu = m_menuBar->addMenu("&File");

    m_newDownloadAction = new QAction("&New Download", this);
    m_newDownloadAction->setShortcut(QKeySequence::New);
    fileMenu->addAction(m_newDownloadAction);

    fileMenu->addSeparator();

    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    fileMenu->addAction(m_exitAction);

    // Downloads menu
    auto* downloadsMenu = m_menuBar->addMenu("&Downloads");

    m_pauseAllAction = new QAction("&Pause All", this);
    downloadsMenu->addAction(m_pauseAllAction);

    m_resumeAllAction = new QAction("&Resume All", this);
    downloadsMenu->addAction(m_resumeAllAction);

    // Tools menu
    auto* toolsMenu = m_menuBar->addMenu("&Tools");

    m_settingsAction = new QAction("&Settings", this);
    toolsMenu->addAction(m_settingsAction);

    // Help menu
    auto* helpMenu = m_menuBar->addMenu("&Help");

    m_aboutAction = new QAction("&About", this);
    helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupToolBar()
{
    m_toolBar = addToolBar("Main");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    m_toolBar->addAction(m_newDownloadAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_pauseAllAction);
    m_toolBar->addAction(m_resumeAllAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_settingsAction);
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();
    m_statusBar->showMessage("Ready");
}

void MainWindow::connectSignals()
{
    // Button connections
    connect(m_startButton, &QPushButton::clicked, this, &MainWindow::onStartDownload);
    connect(m_pauseButton, &QPushButton::clicked, this, &MainWindow::onPauseDownload);
    connect(m_resumeButton, &QPushButton::clicked, this, &MainWindow::onResumeDownload);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::onStopDownload);
    connect(m_browseButton, &QPushButton::clicked, this, &MainWindow::onBrowseOutputPath);
    connect(m_addToQueueButton, &QPushButton::clicked, this, &MainWindow::onAddToQueue);
    connect(m_clearQueueButton, &QPushButton::clicked, this, &MainWindow::onClearQueue);

    // Menu connections
    connect(m_newDownloadAction, &QAction::triggered, [this]() { m_tabWidget->setCurrentIndex(0); });
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::onSettingsClicked);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onAboutClicked);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);

    // URL edit connection
    connect(m_urlEdit, &QLineEdit::returnPressed, this, &MainWindow::onStartDownload);
}

void MainWindow::applyIDMStyle()
{
    // Apply IDM-like styling
    setStyleSheet(R"(
        QMainWindow {
            background-color: #f0f0f0;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QLineEdit {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 11px;
        }
        QLineEdit:focus {
            border-color: #4CAF50;
        }
        QPushButton {
            border: 2px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 11px;
            min-width: 80px;
        }
        QPushButton:hover {
            background-color: #e6e6e6;
        }
        QPushButton:pressed {
            background-color: #d4d4d4;
        }
        QProgressBar {
            border: 2px solid #ddd;
            border-radius: 4px;
            text-align: center;
            font-weight: bold;
        }
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 2px;
        }
        QTabWidget::pane {
            border: 1px solid #ddd;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e6e6e6;
            border: 1px solid #ddd;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }
        QTableWidget {
            gridline-color: #ddd;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }
    )");
}

// Slot implementations
void MainWindow::onStartDownload()
{
    QString url = m_urlEdit->text().trimmed();
    if (url.isEmpty()) {
        QMessageBox::warning(this, "Warning", "Please enter a URL to download.");
        return;
    }

    QString outputPath = m_outputPathEdit->text().trimmed();
    if (outputPath.isEmpty()) {
        QMessageBox::warning(this, "Warning", "Please specify an output path.");
        return;
    }

    // Extract filename from URL if not specified
    if (QDir(outputPath).exists()) {
        QUrl qurl(url);
        QString fileName = qurl.fileName();
        if (fileName.isEmpty()) {
            fileName = "download";
        }
        outputPath = QDir(outputPath).filePath(fileName);
    }

    m_currentUrl = url;
    m_currentOutputPath = outputPath;
    m_isDownloading = true;

    // Update UI state
    m_startButton->setEnabled(false);
    m_pauseButton->setEnabled(true);
    m_stopButton->setEnabled(true);
    m_progressBar->setValue(0);
    m_statusLabel->setText("Starting download...");

    // Log the download start
    m_logTextEdit->append(QString("[%1] Starting download: %2")
                         .arg(QDateTime::currentDateTime().toString())
                         .arg(url));

    // Start download in worker thread
    QMetaObject::invokeMethod(m_downloadWorker, "startDownload",
                             Q_ARG(QString, url),
                             Q_ARG(QString, outputPath),
                             Q_ARG(int, m_connectionsSpinBox->value()));

    m_updateTimer->start(500); // Update every 500ms
}

void MainWindow::onPauseDownload()
{
    if (m_isDownloading) {
        QMetaObject::invokeMethod(m_downloadWorker, "pauseDownload");
        m_pauseButton->setEnabled(false);
        m_resumeButton->setEnabled(true);
        m_statusLabel->setText("Download paused");

        m_logTextEdit->append(QString("[%1] Download paused")
                             .arg(QDateTime::currentDateTime().toString()));
    }
}

void MainWindow::onResumeDownload()
{
    if (m_isDownloading) {
        QMetaObject::invokeMethod(m_downloadWorker, "resumeDownload");
        m_resumeButton->setEnabled(false);
        m_pauseButton->setEnabled(true);
        m_statusLabel->setText("Resuming download...");

        m_logTextEdit->append(QString("[%1] Download resumed")
                             .arg(QDateTime::currentDateTime().toString()));
    }
}

void MainWindow::onStopDownload()
{
    if (m_isDownloading) {
        QMetaObject::invokeMethod(m_downloadWorker, "stopDownload");

        // Reset UI state
        m_isDownloading = false;
        m_startButton->setEnabled(true);
        m_pauseButton->setEnabled(false);
        m_resumeButton->setEnabled(false);
        m_stopButton->setEnabled(false);
        m_progressBar->setValue(0);
        m_statusLabel->setText("Download stopped");
        m_speedLabel->setText("Speed: 0 KB/s");
        m_etaLabel->setText("ETA: --:--");

        m_updateTimer->stop();

        m_logTextEdit->append(QString("[%1] Download stopped")
                             .arg(QDateTime::currentDateTime().toString()));
    }
}
