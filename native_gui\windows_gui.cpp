/*
 * Native Windows GUI for IDM Downloader
 * Uses only Windows API - no external dependencies!
 */

#ifdef _WIN32

#include <windows.h>
#include <commctrl.h>
#include <commdlg.h>
#include <string>
#include <thread>
#include "../include/downloader.h"

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "comdlg32.lib")

// Control IDs
#define ID_URL_EDIT         1001
#define ID_OUTPUT_EDIT      1002
#define ID_BROWSE_BUTTON    1003
#define ID_START_BUTTON     1004
#define ID_PAUSE_BUTTON     1005
#define ID_STOP_BUTTON      1006
#define ID_PROGRESS_BAR     1007
#define ID_STATUS_LABEL     1008
#define ID_SPEED_LABEL      1009
#define ID_CONNECTIONS_SPIN 1010

// Global variables
HWND g_hMainWnd = nullptr;
HWND g_hUrlEdit = nullptr;
HWND g_hOutputEdit = nullptr;
HWND g_hProgressBar = nullptr;
H<PERSON>ND g_hStatusLabel = nullptr;
<PERSON><PERSON><PERSON> g_hSpeedLabel = nullptr;
HWND g_hConnectionsSpin = nullptr;

std::unique_ptr<Downloader> g_downloader;
bool g_isDownloading = false;

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void OnStartDownload();
void OnBrowseOutput();
void UpdateProgress(int percentage, const std::string& status, const std::string& speed);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_PROGRESS_CLASS | ICC_UPDOWN_CLASS;
    InitCommonControlsEx(&icex);
    
    // Register window class
    const char* CLASS_NAME = "IDMDownloaderWindow";
    
    WNDCLASS wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = CLASS_NAME;
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    
    RegisterClass(&wc);
    
    // Create window
    g_hMainWnd = CreateWindowEx(
        0,
        CLASS_NAME,
        "IDM Downloader - Internet Download Manager",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 600, 400,
        nullptr, nullptr, hInstance, nullptr
    );
    
    if (g_hMainWnd == nullptr) {
        return 0;
    }
    
    ShowWindow(g_hMainWnd, nCmdShow);
    UpdateWindow(g_hMainWnd);
    
    // Initialize downloader
    g_downloader = std::make_unique<Downloader>();
    
    // Message loop
    MSG msg = {};
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg) {
    case WM_CREATE:
        CreateControls(hwnd);
        break;
        
    case WM_COMMAND:
        switch (LOWORD(wParam)) {
        case ID_START_BUTTON:
            OnStartDownload();
            break;
        case ID_BROWSE_BUTTON:
            OnBrowseOutput();
            break;
        case ID_PAUSE_BUTTON:
            // TODO: Implement pause
            break;
        case ID_STOP_BUTTON:
            // TODO: Implement stop
            break;
        }
        break;
        
    case WM_SIZE:
        // TODO: Resize controls
        break;
        
    case WM_DESTROY:
        PostQuitMessage(0);
        break;
        
    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

void CreateControls(HWND hwnd)
{
    HFONT hFont = CreateFont(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
        DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
        DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE, "Segoe UI");
    
    // Title
    HWND hTitle = CreateWindow("STATIC", "🚀 IDM Downloader - Professional Download Manager",
        WS_VISIBLE | WS_CHILD | SS_CENTER,
        10, 10, 560, 30, hwnd, nullptr, nullptr, nullptr);
    SendMessage(hTitle, WM_SETFONT, (WPARAM)hFont, TRUE);
    
    // URL group
    CreateWindow("STATIC", "Download URL:",
        WS_VISIBLE | WS_CHILD,
        20, 50, 100, 20, hwnd, nullptr, nullptr, nullptr);
    
    g_hUrlEdit = CreateWindow("EDIT", "",
        WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL,
        20, 70, 450, 25, hwnd, (HMENU)ID_URL_EDIT, nullptr, nullptr);
    SendMessage(g_hUrlEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
    
    // Output path group
    CreateWindow("STATIC", "Save to:",
        WS_VISIBLE | WS_CHILD,
        20, 110, 100, 20, hwnd, nullptr, nullptr, nullptr);
    
    g_hOutputEdit = CreateWindow("EDIT", "",
        WS_VISIBLE | WS_CHILD | WS_BORDER | ES_AUTOHSCROLL,
        20, 130, 350, 25, hwnd, (HMENU)ID_OUTPUT_EDIT, nullptr, nullptr);
    SendMessage(g_hOutputEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
    
    CreateWindow("BUTTON", "Browse...",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        380, 130, 80, 25, hwnd, (HMENU)ID_BROWSE_BUTTON, nullptr, nullptr);
    
    // Connections
    CreateWindow("STATIC", "Connections:",
        WS_VISIBLE | WS_CHILD,
        480, 110, 80, 20, hwnd, nullptr, nullptr, nullptr);
    
    g_hConnectionsSpin = CreateWindow("EDIT", "4",
        WS_VISIBLE | WS_CHILD | WS_BORDER | ES_NUMBER,
        480, 130, 50, 25, hwnd, (HMENU)ID_CONNECTIONS_SPIN, nullptr, nullptr);
    
    // Progress bar
    CreateWindow("STATIC", "Progress:",
        WS_VISIBLE | WS_CHILD,
        20, 170, 100, 20, hwnd, nullptr, nullptr, nullptr);
    
    g_hProgressBar = CreateWindow(PROGRESS_CLASS, "",
        WS_VISIBLE | WS_CHILD,
        20, 190, 540, 25, hwnd, (HMENU)ID_PROGRESS_BAR, nullptr, nullptr);
    SendMessage(g_hProgressBar, PBM_SETRANGE, 0, MAKELPARAM(0, 100));
    
    // Status labels
    g_hStatusLabel = CreateWindow("STATIC", "Status: Ready",
        WS_VISIBLE | WS_CHILD,
        20, 230, 200, 20, hwnd, (HMENU)ID_STATUS_LABEL, nullptr, nullptr);
    
    g_hSpeedLabel = CreateWindow("STATIC", "Speed: 0 KB/s",
        WS_VISIBLE | WS_CHILD,
        250, 230, 150, 20, hwnd, (HMENU)ID_SPEED_LABEL, nullptr, nullptr);
    
    // Control buttons
    CreateWindow("BUTTON", "Start Download",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        20, 270, 120, 35, hwnd, (HMENU)ID_START_BUTTON, nullptr, nullptr);
    
    CreateWindow("BUTTON", "Pause",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        150, 270, 80, 35, hwnd, (HMENU)ID_PAUSE_BUTTON, nullptr, nullptr);
    
    CreateWindow("BUTTON", "Stop",
        WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
        240, 270, 80, 35, hwnd, (HMENU)ID_STOP_BUTTON, nullptr, nullptr);
    
    // Set default output path
    char downloads[MAX_PATH];
    if (SHGetFolderPath(nullptr, CSIDL_MYDOCUMENTS, nullptr, SHGFP_TYPE_CURRENT, downloads) == S_OK) {
        strcat(downloads, "\\Downloads");
        SetWindowText(g_hOutputEdit, downloads);
    }
}

void OnStartDownload()
{
    char url[1024];
    char outputPath[MAX_PATH];
    char connections[10];
    
    GetWindowText(g_hUrlEdit, url, sizeof(url));
    GetWindowText(g_hOutputEdit, outputPath, sizeof(outputPath));
    GetWindowText(g_hConnectionsSpin, connections, sizeof(connections));
    
    if (strlen(url) == 0) {
        MessageBox(g_hMainWnd, "Please enter a URL to download.", "Warning", MB_OK | MB_ICONWARNING);
        return;
    }
    
    if (strlen(outputPath) == 0) {
        MessageBox(g_hMainWnd, "Please specify an output path.", "Warning", MB_OK | MB_ICONWARNING);
        return;
    }
    
    g_isDownloading = true;
    
    // Update UI
    EnableWindow(GetDlgItem(g_hMainWnd, ID_START_BUTTON), FALSE);
    SetWindowText(g_hStatusLabel, "Status: Starting download...");
    SendMessage(g_hProgressBar, PBM_SETPOS, 0, 0);
    
    // Start download in background thread
    std::thread([url, outputPath, connections]() {
        DownloadConfig config;
        config.url = url;
        config.output_path = outputPath;
        config.num_connections = atoi(connections);
        config.resume = true;
        
        auto progress_cb = [](const DownloadStats& stats) {
            int percentage = 0;
            if (stats.total_size > 0) {
                percentage = static_cast<int>((stats.downloaded_size * 100) / stats.total_size);
            }
            
            std::string status = "Status: Downloading... " + std::to_string(percentage) + "%";
            std::string speed = "Speed: " + std::to_string(static_cast<int>(stats.download_speed / 1024)) + " KB/s";
            
            // Update UI on main thread
            PostMessage(g_hMainWnd, WM_USER + 1, percentage, 0);
        };
        
        auto completion_cb = [](bool success, const std::string& error) {
            PostMessage(g_hMainWnd, WM_USER + 2, success ? 1 : 0, 0);
        };
        
        try {
            g_downloader->downloadAsync(config, progress_cb, completion_cb);
        } catch (const std::exception& e) {
            PostMessage(g_hMainWnd, WM_USER + 2, 0, 0);
        }
    }).detach();
}

void OnBrowseOutput()
{
    OPENFILENAME ofn;
    char szFile[MAX_PATH] = "";
    
    ZeroMemory(&ofn, sizeof(ofn));
    ofn.lStructSize = sizeof(ofn);
    ofn.hwndOwner = g_hMainWnd;
    ofn.lpstrFile = szFile;
    ofn.nMaxFile = sizeof(szFile);
    ofn.lpstrFilter = "All Files\0*.*\0";
    ofn.nFilterIndex = 1;
    ofn.lpstrFileTitle = nullptr;
    ofn.nMaxFileTitle = 0;
    ofn.lpstrInitialDir = nullptr;
    ofn.Flags = OFN_PATHMUSTEXIST | OFN_OVERWRITEPROMPT;
    
    if (GetSaveFileName(&ofn)) {
        SetWindowText(g_hOutputEdit, szFile);
    }
}

#else

#include <iostream>

int main() {
    std::cout << "This GUI is only available on Windows." << std::endl;
    return 0;
}

#endif
