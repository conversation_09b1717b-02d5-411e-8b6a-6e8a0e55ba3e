#!/bin/bash
echo "Building IDM Downloader Web GUI..."

# Check if g++ is available
if ! command -v g++ &> /dev/null; then
    echo "❌ g++ not found. Please install with:"
    echo "   pacman -S mingw-w64-x86_64-gcc"
    exit 1
fi

# Check if curl is available
if ! pkg-config --exists libcurl; then
    echo "❌ libcurl not found. Please install with:"
    echo "   pacman -S mingw-w64-x86_64-curl"
    exit 1
fi

echo "Compiling web server..."

# Compile the web GUI
g++ -std=c++17 -I include \
    web_gui/web_server.cpp \
    src/downloader.cpp \
    src/http_client.cpp \
    src/file_manager.cpp \
    src/progress_tracker.cpp \
    src/utils.cpp \
    -lcurl -lws2_32 -lwldap32 \
    -o IDMDownloaderWebGUI.exe

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Web GUI build successful!"
    echo ""
    echo "🌐 To run the Web GUI:"
    echo "   1. Run: ./IDMDownloaderWebGUI.exe"
    echo "   2. Open your browser to: http://localhost:8080"
    echo "   3. Use the web interface to download files"
    echo ""
    echo "📱 The web interface works on any device with a browser!"
    echo ""
else
    echo ""
    echo "❌ Build failed!"
    echo "Make sure you have:"
    echo "   - GCC with C++17 support"
    echo "   - libcurl development libraries"
    echo "   - Windows Sockets library"
fi
