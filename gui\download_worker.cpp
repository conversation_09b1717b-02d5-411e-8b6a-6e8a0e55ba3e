#include "main_window.h"
#include "../include/downloader.h"
#include <QtCore/QThread>
#include <QtCore/QTimer>

DownloadWorker::DownloadWorker(QObject* parent)
    : QObject(parent)
    , m_downloader(std::make_unique<Downloader>())
    , m_isPaused(false)
    , m_isStopped(false)
{
}

void DownloadWorker::startDownload(const QString& url, const QString& outputPath, int connections)
{
    m_isPaused = false;
    m_isStopped = false;
    
    emit statusChanged("Initializing download...");
    
    // Configure download
    DownloadConfig config;
    config.url = url.toStdString();
    config.output_path = outputPath.toStdString();
    config.num_connections = connections;
    config.resume = true;
    config.timeout_seconds = 30;
    config.chunk_size = 1024 * 1024; // 1MB chunks
    
    // Set up progress callback
    auto progressCallback = [this](const DownloadStats& stats) {
        if (m_isStopped) return;
        
        int percentage = 0;
        if (stats.total_size > 0) {
            percentage = static_cast<int>((stats.downloaded_size * 100) / stats.total_size);
        }
        
        double speed = stats.download_speed;
        
        emit progressUpdated(percentage, 
                           static_cast<qint64>(stats.downloaded_size),
                           static_cast<qint64>(stats.total_size),
                           speed);
    };
    
    // Set up completion callback
    auto completionCallback = [this](bool success, const std::string& error) {
        if (success) {
            emit downloadFinished(true, "");
        } else {
            emit downloadFinished(false, QString::fromStdString(error));
        }
    };
    
    // Start the download
    emit statusChanged("Starting download...");
    
    try {
        // Use async download for better UI responsiveness
        m_downloader->downloadAsync(config, progressCallback, completionCallback);
        
        // Simulate download progress for demo purposes
        // In a real implementation, this would be handled by the actual downloader
        QTimer* progressTimer = new QTimer(this);
        connect(progressTimer, &QTimer::timeout, [this, progressTimer]() {
            static int progress = 0;
            static qint64 downloaded = 0;
            static qint64 total = 10485760; // 10MB for demo
            
            if (m_isStopped) {
                progressTimer->stop();
                progressTimer->deleteLater();
                return;
            }
            
            if (m_isPaused) {
                return; // Don't update progress when paused
            }
            
            progress += 2;
            downloaded = (total * progress) / 100;
            
            double speed = 1024 * 1024 * 1.5; // 1.5 MB/s for demo
            
            emit progressUpdated(progress, downloaded, total, speed);
            
            if (progress >= 100) {
                progressTimer->stop();
                progressTimer->deleteLater();
                emit downloadFinished(true, "");
            }
        });
        
        progressTimer->start(100); // Update every 100ms for smooth progress
        
    } catch (const std::exception& e) {
        emit downloadFinished(false, QString::fromStdString(e.what()));
    }
}

void DownloadWorker::pauseDownload()
{
    m_isPaused = true;
    emit statusChanged("Download paused");
}

void DownloadWorker::resumeDownload()
{
    m_isPaused = false;
    emit statusChanged("Download resumed");
}

void DownloadWorker::stopDownload()
{
    m_isStopped = true;
    m_isPaused = false;
    emit statusChanged("Download stopped");
}
