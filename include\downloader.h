#pragma once

#include <string>
#include <memory>
#include <functional>
#include <atomic>
#include <thread>
#include <vector>

class HttpClient;
class ProgressTracker;
class FileManager;

struct DownloadConfig {
    std::string url;
    std::string output_path;
    int max_connections = 4;
    bool resume_download = true;
    bool show_progress = true;
    size_t chunk_size = 1024 * 1024; // 1MB chunks
    int timeout_seconds = 30;
    std::string user_agent = "IDM-Downloader/1.0";
};

struct DownloadStats {
    size_t total_size = 0;
    size_t downloaded_size = 0;
    double download_speed = 0.0; // bytes per second
    double progress_percentage = 0.0;
    std::string eta; // estimated time of arrival
    bool is_complete = false;
    bool has_error = false;
    std::string error_message;
};

class Downloader {
public:
    using ProgressCallback = std::function<void(const DownloadStats&)>;
    using CompletionCallback = std::function<void(bool success, const std::string& error)>;

    Downloader();
    ~Downloader();

    // Main download function
    bool download(const DownloadConfig& config);
    
    // Async download
    void downloadAsync(const DownloadConfig& config, 
                      ProgressCallback progress_cb = nullptr,
                      CompletionCallback completion_cb = nullptr);

    // Control functions
    void pause();
    void resume();
    void cancel();
    
    // Status functions
    bool isDownloading() const;
    bool isPaused() const;
    DownloadStats getStats() const;

private:
    std::unique_ptr<HttpClient> http_client_;
    std::unique_ptr<ProgressTracker> progress_tracker_;
    std::unique_ptr<FileManager> file_manager_;
    
    std::atomic<bool> is_downloading_{false};
    std::atomic<bool> is_paused_{false};
    std::atomic<bool> should_cancel_{false};
    
    std::thread download_thread_;
    DownloadStats current_stats_;
    mutable std::mutex stats_mutex_;

    // Internal download implementation
    bool downloadInternal(const DownloadConfig& config,
                         ProgressCallback progress_cb,
                         CompletionCallback completion_cb);
    
    // Multi-threaded download
    bool downloadMultiThreaded(const DownloadConfig& config,
                              ProgressCallback progress_cb);
    
    // Single-threaded download
    bool downloadSingleThreaded(const DownloadConfig& config,
                               ProgressCallback progress_cb);
    
    // Helper functions
    void updateStats(size_t downloaded, size_t total);
    void notifyProgress(ProgressCallback callback);
    bool supportsRangeRequests(const std::string& url);
};
