#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <signal.h>

#include "downloader.h"
#include "utils.h"

// Global downloader instance for signal handling
Downloader* g_downloader = nullptr;

void signalHandler(int signal) {
    if (g_downloader && signal == SIGINT) {
        std::cout << "\n\nReceived interrupt signal. Cancelling download...\n";
        g_downloader->cancel();
    }
}

void printUsage(const std::string& program_name) {
    std::cout << "IDM Downloader - A C++ HTTP/HTTPS Downloader\n\n";
    std::cout << "Usage: " << program_name << " [OPTIONS] URL [OUTPUT_FILE]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -h, --help              Show this help message\n";
    std::cout << "  -o, --output FILE       Output file path\n";
    std::cout << "  -c, --connections N     Number of parallel connections (default: 4)\n";
    std::cout << "  -r, --resume            Resume incomplete download (default: true)\n";
    std::cout << "  --no-resume             Disable resume functionality\n";
    std::cout << "  -q, --quiet             Quiet mode (no progress display)\n";
    std::cout << "  -t, --timeout N         Timeout in seconds (default: 30)\n";
    std::cout << "  -u, --user-agent STR    Custom user agent string\n";
    std::cout << "  --chunk-size N          Chunk size in bytes (default: 1MB)\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << " https://example.com/file.zip\n";
    std::cout << "  " << program_name << " -o myfile.zip -c 8 https://example.com/file.zip\n";
    std::cout << "  " << program_name << " --no-resume -q https://example.com/file.zip\n";
}

struct CommandLineArgs {
    std::string url;
    std::string output_file;
    int connections = 4;
    bool resume = true;
    bool show_progress = true;
    int timeout = 30;
    std::string user_agent = "IDM-Downloader/1.0";
    size_t chunk_size = 1024 * 1024; // 1MB
    bool help = false;
};

CommandLineArgs parseArgs(int argc, char* argv[]) {
    CommandLineArgs args;
    
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            args.help = true;
        } else if (arg == "-o" || arg == "--output") {
            if (i + 1 < argc) {
                args.output_file = argv[++i];
            }
        } else if (arg == "-c" || arg == "--connections") {
            if (i + 1 < argc) {
                args.connections = std::stoi(argv[++i]);
            }
        } else if (arg == "-r" || arg == "--resume") {
            args.resume = true;
        } else if (arg == "--no-resume") {
            args.resume = false;
        } else if (arg == "-q" || arg == "--quiet") {
            args.show_progress = false;
        } else if (arg == "-t" || arg == "--timeout") {
            if (i + 1 < argc) {
                args.timeout = std::stoi(argv[++i]);
            }
        } else if (arg == "-u" || arg == "--user-agent") {
            if (i + 1 < argc) {
                args.user_agent = argv[++i];
            }
        } else if (arg == "--chunk-size") {
            if (i + 1 < argc) {
                args.chunk_size = std::stoull(argv[++i]);
            }
        } else if (arg[0] != '-') {
            if (args.url.empty()) {
                args.url = arg;
            } else if (args.output_file.empty()) {
                args.output_file = arg;
            }
        }
    }
    
    return args;
}

int main(int argc, char* argv[]) {
    CommandLineArgs args = parseArgs(argc, argv);
    
    if (args.help || args.url.empty()) {
        printUsage(argv[0]);
        return args.help ? 0 : 1;
    }
    
    // Validate URL
    if (!Utils::isValidUrl(args.url)) {
        std::cerr << "Error: Invalid URL provided\n";
        return 1;
    }
    
    // Generate output filename if not provided
    if (args.output_file.empty()) {
        args.output_file = Utils::extractFileName(args.url);
        if (args.output_file.empty()) {
            args.output_file = "downloaded_file";
        }
    }
    
    // Setup signal handler
    signal(SIGINT, signalHandler);
    
    // Create downloader
    Downloader downloader;
    g_downloader = &downloader;
    
    // Configure download
    DownloadConfig config;
    config.url = args.url;
    config.output_path = args.output_file;
    config.max_connections = args.connections;
    config.resume_download = args.resume;
    config.show_progress = args.show_progress;
    config.chunk_size = args.chunk_size;
    config.timeout_seconds = args.timeout;
    config.user_agent = args.user_agent;
    
    std::cout << "Starting download...\n";
    std::cout << "URL: " << config.url << "\n";
    std::cout << "Output: " << config.output_path << "\n";
    std::cout << "Connections: " << config.max_connections << "\n\n";
    
    // Progress callback
    auto progress_callback = [&](const DownloadStats& stats) {
        if (config.show_progress) {
            Utils::clearLine();
            std::cout << "\r";
            
            std::string progress_bar = Utils::createProgressBar(stats.progress_percentage);
            std::cout << progress_bar << " " 
                     << Utils::formatPercentage(stats.progress_percentage) << " "
                     << Utils::formatBytes(stats.downloaded_size) << "/"
                     << Utils::formatBytes(stats.total_size) << " "
                     << Utils::formatSpeed(stats.download_speed);
            
            if (!stats.eta.empty()) {
                std::cout << " ETA: " << stats.eta;
            }
            
            std::cout.flush();
        }
    };
    
    // Completion callback
    auto completion_callback = [&](bool success, const std::string& error) {
        if (config.show_progress) {
            std::cout << "\n";
        }
        
        if (success) {
            std::cout << "Download completed successfully!\n";
            std::cout << "File saved to: " << config.output_path << "\n";
        } else {
            std::cerr << "Download failed: " << error << "\n";
        }
    };
    
    // Start download
    bool success = downloader.download(config);
    
    if (success) {
        std::cout << "\nDownload completed successfully!\n";
        return 0;
    } else {
        std::cerr << "\nDownload failed!\n";
        return 1;
    }
}
