#include <QtWidgets/QApplication>
#include <QtCore/QDir>
#include <QtCore/QStandardPaths>
#include <QtGui/QIcon>
#include "main_window.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("IDM Downloader");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("IDM Downloader Project");
    app.setApplicationDisplayName("IDM Downloader - Internet Download Manager");
    
    // Set application icon
    app.setWindowIcon(QIcon(":/icons/idm_icon.png"));
    
    // Create and show main window
    MainWindow window;
    window.show();
    
    return app.exec();
}
