#pragma once

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QLabel>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QAction>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTreeWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTabWidget>
#include <QtCore/QTimer>
#include <QtCore/QThread>
#include <memory>

class Downloader;
class DownloadWorker;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onStartDownload();
    void onPauseDownload();
    void onStopDownload();
    void onResumeDownload();
    void onBrowseOutputPath();
    void onAddToQueue();
    void onClearQueue();
    void onSettingsClicked();
    void onAboutClicked();
    void updateProgress(int percentage, qint64 downloaded, qint64 total, double speed);
    void downloadFinished(bool success, const QString& message);
    void updateDownloadList();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupMainArea();
    void setupDownloadArea();
    void setupQueueArea();
    void setupLogArea();
    void connectSignals();
    void applyIDMStyle();
    
    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    QTabWidget* m_tabWidget;
    
    // Download Area
    QWidget* m_downloadWidget;
    QLineEdit* m_urlEdit;
    QLineEdit* m_outputPathEdit;
    QPushButton* m_browseButton;
    QPushButton* m_startButton;
    QPushButton* m_pauseButton;
    QPushButton* m_stopButton;
    QPushButton* m_resumeButton;
    QPushButton* m_addToQueueButton;
    
    QSpinBox* m_connectionsSpinBox;
    QComboBox* m_categoryComboBox;
    QCheckBox* m_resumeCheckBox;
    QCheckBox* m_shutdownCheckBox;
    
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    QLabel* m_speedLabel;
    QLabel* m_etaLabel;
    QLabel* m_fileSizeLabel;
    
    // Queue Area
    QTableWidget* m_queueTable;
    QPushButton* m_clearQueueButton;
    QPushButton* m_startQueueButton;
    
    // Log Area
    QTextEdit* m_logTextEdit;
    
    // Menu and Toolbar
    QMenuBar* m_menuBar;
    QToolBar* m_toolBar;
    QStatusBar* m_statusBar;
    
    QAction* m_newDownloadAction;
    QAction* m_pauseAllAction;
    QAction* m_resumeAllAction;
    QAction* m_settingsAction;
    QAction* m_exitAction;
    QAction* m_aboutAction;
    
    // Backend
    std::unique_ptr<Downloader> m_downloader;
    QThread* m_downloadThread;
    DownloadWorker* m_downloadWorker;
    QTimer* m_updateTimer;
    
    // State
    bool m_isDownloading;
    QString m_currentUrl;
    QString m_currentOutputPath;
};

class DownloadWorker : public QObject
{
    Q_OBJECT

public:
    DownloadWorker(QObject* parent = nullptr);

public slots:
    void startDownload(const QString& url, const QString& outputPath, int connections);
    void pauseDownload();
    void resumeDownload();
    void stopDownload();

signals:
    void progressUpdated(int percentage, qint64 downloaded, qint64 total, double speed);
    void downloadFinished(bool success, const QString& message);
    void statusChanged(const QString& status);

private:
    std::unique_ptr<Downloader> m_downloader;
    bool m_isPaused;
    bool m_isStopped;
};

#include "main_window.moc"
