#pragma once

#include <string>
#include <vector>
#include <map>
#include <functional>
#include <memory>

struct HttpResponse {
    long status_code = 0;
    std::map<std::string, std::string> headers;
    std::vector<char> body;
    std::string error_message;
    bool success = false;
};

struct HttpRequest {
    std::string url;
    std::string method = "GET";
    std::map<std::string, std::string> headers;
    std::vector<char> body;
    int timeout_seconds = 30;
    bool follow_redirects = true;
    std::string user_agent = "IDM-Downloader/1.0";
    
    // Range request support
    bool use_range = false;
    size_t range_start = 0;
    size_t range_end = 0;
};

class HttpClient {
public:
    using ProgressCallback = std::function<bool(size_t downloaded, size_t total)>;
    using WriteCallback = std::function<size_t(const char* data, size_t size)>;

    HttpClient();
    ~HttpClient();

    // Basic HTTP operations
    HttpResponse get(const std::string& url, 
                    const std::map<std::string, std::string>& headers = {});
    
    HttpResponse head(const std::string& url,
                     const std::map<std::string, std::string>& headers = {});
    
    // Download with callbacks
    bool download(const HttpRequest& request,
                 WriteCallback write_cb,
                 ProgressCallback progress_cb = nullptr);
    
    // Range request download
    bool downloadRange(const std::string& url,
                      size_t start,
                      size_t end,
                      WriteCallback write_cb,
                      ProgressCallback progress_cb = nullptr);
    
    // Utility functions
    bool supportsRangeRequests(const std::string& url);
    size_t getContentLength(const std::string& url);
    std::string getContentType(const std::string& url);
    
    // Configuration
    void setTimeout(int seconds);
    void setUserAgent(const std::string& user_agent);
    void setFollowRedirects(bool follow);
    void setMaxRedirects(int max_redirects);

private:
    class Impl;
    std::unique_ptr<Impl> pimpl_;
};
