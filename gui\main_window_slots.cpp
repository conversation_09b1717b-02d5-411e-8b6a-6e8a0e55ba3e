// Additional slot implementations for MainWindow
#include "main_window.h"
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QMessageBox>
#include <QtCore/QStandardPaths>
#include <QtCore/QDateTime>

void MainWindow::onBrowseOutputPath()
{
    QString currentPath = m_outputPathEdit->text();
    if (currentPath.isEmpty()) {
        currentPath = QStandardPaths::writableLocation(QStandardPaths::DownloadLocation);
    }
    
    QString fileName = QFileDialog::getSaveFileName(this,
        "Save Download As", currentPath, "All Files (*.*)");
    
    if (!fileName.isEmpty()) {
        m_outputPathEdit->setText(fileName);
    }
}

void MainWindow::onAddToQueue()
{
    QString url = m_urlEdit->text().trimmed();
    if (url.isEmpty()) {
        QMessageBox::warning(this, "Warning", "Please enter a URL to add to queue.");
        return;
    }
    
    QString outputPath = m_outputPathEdit->text().trimmed();
    if (outputPath.isEmpty()) {
        outputPath = QStandardPaths::writableLocation(QStandardPaths::DownloadLocation);
    }
    
    // Add to queue table
    int row = m_queueTable->rowCount();
    m_queueTable->insertRow(row);
    
    QUrl qurl(url);
    QString fileName = qurl.fileName();
    if (fileName.isEmpty()) {
        fileName = QString("download_%1").arg(row + 1);
    }
    
    m_queueTable->setItem(row, 0, new QTableWidgetItem(url));
    m_queueTable->setItem(row, 1, new QTableWidgetItem(fileName));
    m_queueTable->setItem(row, 2, new QTableWidgetItem("Unknown"));
    m_queueTable->setItem(row, 3, new QTableWidgetItem("0%"));
    m_queueTable->setItem(row, 4, new QTableWidgetItem("0 KB/s"));
    m_queueTable->setItem(row, 5, new QTableWidgetItem("Queued"));
    
    // Clear input fields
    m_urlEdit->clear();
    
    // Switch to queue tab
    m_tabWidget->setCurrentIndex(1);
    
    m_logTextEdit->append(QString("[%1] Added to queue: %2")
                         .arg(QDateTime::currentDateTime().toString())
                         .arg(url));
}

void MainWindow::onClearQueue()
{
    if (m_queueTable->rowCount() > 0) {
        int ret = QMessageBox::question(this, "Clear Queue",
            "Are you sure you want to clear all items from the queue?",
            QMessageBox::Yes | QMessageBox::No);
        
        if (ret == QMessageBox::Yes) {
            m_queueTable->setRowCount(0);
            m_logTextEdit->append(QString("[%1] Queue cleared")
                                 .arg(QDateTime::currentDateTime().toString()));
        }
    }
}

void MainWindow::onSettingsClicked()
{
    QMessageBox::information(this, "Settings", 
        "Settings dialog will be implemented in the next version.\n\n"
        "Current settings:\n"
        "• Default connections: 4\n"
        "• Resume downloads: Enabled\n"
        "• Download timeout: 30 seconds");
}

void MainWindow::onAboutClicked()
{
    QMessageBox::about(this, "About IDM Downloader",
        "<h3>IDM Downloader v1.0</h3>"
        "<p>A professional multi-threaded download manager built with C++ and Qt.</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Multi-threaded downloads (up to 16 connections)</li>"
        "<li>Resume interrupted downloads</li>"
        "<li>Real-time progress tracking</li>"
        "<li>HTTP/HTTPS support with SSL</li>"
        "<li>Download queue management</li>"
        "<li>Cross-platform compatibility</li>"
        "</ul>"
        "<p><b>Built with:</b> C++17, Qt6, libcurl</p>"
        "<p>© 2024 IDM Downloader Project</p>");
}

void MainWindow::updateProgress(int percentage, qint64 downloaded, qint64 total, double speed)
{
    m_progressBar->setValue(percentage);
    
    // Format file sizes
    auto formatBytes = [](qint64 bytes) -> QString {
        if (bytes < 1024) return QString("%1 B").arg(bytes);
        if (bytes < 1024 * 1024) return QString("%.1f KB").arg(bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return QString("%.1f MB").arg(bytes / (1024.0 * 1024.0));
        return QString("%.1f GB").arg(bytes / (1024.0 * 1024.0 * 1024.0));
    };
    
    // Update labels
    m_speedLabel->setText(QString("Speed: %1/s").arg(formatBytes(static_cast<qint64>(speed))));
    m_fileSizeLabel->setText(QString("Size: %1 / %2").arg(formatBytes(downloaded), formatBytes(total)));
    
    // Calculate ETA
    if (speed > 0 && total > downloaded) {
        qint64 remaining = total - downloaded;
        int eta_seconds = static_cast<int>(remaining / speed);
        int hours = eta_seconds / 3600;
        int minutes = (eta_seconds % 3600) / 60;
        int seconds = eta_seconds % 60;
        
        if (hours > 0) {
            m_etaLabel->setText(QString("ETA: %1:%2:%3")
                               .arg(hours, 2, 10, QChar('0'))
                               .arg(minutes, 2, 10, QChar('0'))
                               .arg(seconds, 2, 10, QChar('0')));
        } else {
            m_etaLabel->setText(QString("ETA: %1:%2")
                               .arg(minutes, 2, 10, QChar('0'))
                               .arg(seconds, 2, 10, QChar('0')));
        }
    } else {
        m_etaLabel->setText("ETA: --:--");
    }
    
    // Update status
    m_statusLabel->setText(QString("Downloading... %1%").arg(percentage));
}

void MainWindow::downloadFinished(bool success, const QString& message)
{
    m_updateTimer->stop();
    
    // Reset UI state
    m_isDownloading = false;
    m_startButton->setEnabled(true);
    m_pauseButton->setEnabled(false);
    m_resumeButton->setEnabled(false);
    m_stopButton->setEnabled(false);
    
    if (success) {
        m_progressBar->setValue(100);
        m_statusLabel->setText("Download completed successfully!");
        m_speedLabel->setText("Speed: 0 KB/s");
        m_etaLabel->setText("ETA: Complete");
        
        m_logTextEdit->append(QString("[%1] Download completed: %2")
                             .arg(QDateTime::currentDateTime().toString())
                             .arg(m_currentOutputPath));
        
        // Show completion message
        QMessageBox::information(this, "Download Complete",
            QString("Download completed successfully!\n\nFile saved to:\n%1")
            .arg(m_currentOutputPath));
        
        // Check if shutdown is requested
        if (m_shutdownCheckBox->isChecked()) {
            QMessageBox::information(this, "Shutdown",
                "Download completed. The system will shutdown in 10 seconds.\n"
                "Close this dialog to cancel shutdown.");
        }
    } else {
        m_statusLabel->setText("Download failed!");
        m_logTextEdit->append(QString("[%1] Download failed: %2")
                             .arg(QDateTime::currentDateTime().toString())
                             .arg(message));
        
        QMessageBox::critical(this, "Download Failed",
            QString("Download failed!\n\nError: %1").arg(message));
    }
}

void MainWindow::updateDownloadList()
{
    // This would update the download list in a real implementation
    // For now, we'll just ensure the UI stays responsive
    QApplication::processEvents();
}
