#include "downloader.h"
#include "http_client.h"
#include "progress_tracker.h"
#include "file_manager.h"
#include "utils.h"
#include <iostream>
#include <thread>
#include <vector>
#include <future>
#include <algorithm>

Downloader::Downloader() 
    : http_client_(std::make_unique<HttpClient>()),
      progress_tracker_(std::make_unique<ProgressTracker>()),
      file_manager_(std::make_unique<FileManager>()) {
}

Downloader::~Downloader() {
    cancel();
    if (download_thread_.joinable()) {
        download_thread_.join();
    }
}

bool Downloader::download(const DownloadConfig& config) {
    return downloadInternal(config, nullptr, nullptr);
}

void Downloader::downloadAsync(const DownloadConfig& config,
                              ProgressCallback progress_cb,
                              CompletionCallback completion_cb) {
    if (is_downloading_) {
        if (completion_cb) {
            completion_cb(false, "Download already in progress");
        }
        return;
    }
    
    // Start download in separate thread
    download_thread_ = std::thread([this, config, progress_cb, completion_cb]() {
        bool success = downloadInternal(config, progress_cb, completion_cb);
        if (completion_cb) {
            completion_cb(success, success ? "" : current_stats_.error_message);
        }
    });
}

void Downloader::pause() {
    is_paused_ = true;
    progress_tracker_->pause();
}

void Downloader::resume() {
    is_paused_ = false;
    progress_tracker_->resume();
}

void Downloader::cancel() {
    should_cancel_ = true;
    is_downloading_ = false;
}

bool Downloader::isDownloading() const {
    return is_downloading_;
}

bool Downloader::isPaused() const {
    return is_paused_;
}

DownloadStats Downloader::getStats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return current_stats_;
}

bool Downloader::downloadInternal(const DownloadConfig& config,
                                 ProgressCallback progress_cb,
                                 CompletionCallback completion_cb) {
    // Reset state
    is_downloading_ = true;
    is_paused_ = false;
    should_cancel_ = false;
    
    // Reset stats
    {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        current_stats_ = DownloadStats{};
    }
    
    try {
        // Configure HTTP client
        http_client_->setTimeout(config.timeout_seconds);
        http_client_->setUserAgent(config.user_agent);
        
        // Get file information
        size_t content_length = http_client_->getContentLength(config.url);
        if (content_length == 0) {
            updateStats(0, 0);
            current_stats_.has_error = true;
            current_stats_.error_message = "Could not determine file size";
            is_downloading_ = false;
            return false;
        }
        
        // Update total size
        updateStats(0, content_length);
        progress_tracker_->setTotalSize(content_length);
        progress_tracker_->start();
        
        // Check if we can resume
        size_t resume_offset = 0;
        if (config.resume_download && file_manager_->canResume(config.output_path)) {
            resume_offset = file_manager_->getExistingFileSize(config.output_path);
            if (resume_offset >= content_length) {
                // File is already complete
                updateStats(content_length, content_length);
                current_stats_.is_complete = true;
                is_downloading_ = false;
                return true;
            }
        }
        
        // Open/create output file
        bool file_opened = false;
        if (resume_offset > 0) {
            file_opened = file_manager_->openFile(config.output_path, true);
            updateStats(resume_offset, content_length);
            progress_tracker_->updateProgress(resume_offset, content_length);
        } else {
            file_opened = file_manager_->createFile(config.output_path, content_length);
        }
        
        if (!file_opened) {
            current_stats_.has_error = true;
            current_stats_.error_message = "Could not open output file";
            is_downloading_ = false;
            return false;
        }
        
        // Determine download strategy
        bool use_multithread = (config.max_connections > 1) && 
                              supportsRangeRequests(config.url) &&
                              (content_length > config.chunk_size * 2);
        
        bool success = false;
        if (use_multithread) {
            success = downloadMultiThreaded(config, progress_cb);
        } else {
            success = downloadSingleThreaded(config, progress_cb);
        }
        
        if (success) {
            file_manager_->finalizeFile();
            current_stats_.is_complete = true;
        }
        
        file_manager_->closeFile();
        is_downloading_ = false;
        
        return success;
        
    } catch (const std::exception& e) {
        current_stats_.has_error = true;
        current_stats_.error_message = e.what();
        is_downloading_ = false;
        return false;
    }
}

bool Downloader::downloadMultiThreaded(const DownloadConfig& config,
                                       ProgressCallback progress_cb) {
    size_t total_size = current_stats_.total_size;
    size_t downloaded_size = current_stats_.downloaded_size;
    size_t remaining_size = total_size - downloaded_size;
    
    // Setup chunks
    int num_chunks = std::min(config.max_connections, 
                             static_cast<int>(remaining_size / config.chunk_size) + 1);
    
    if (!file_manager_->setupChunks(remaining_size, num_chunks)) {
        current_stats_.has_error = true;
        current_stats_.error_message = "Failed to setup chunks";
        return false;
    }
    
    auto chunks = file_manager_->getChunks();
    std::vector<std::future<bool>> futures;
    std::atomic<size_t> total_downloaded{downloaded_size};
    
    // Start download threads
    for (size_t i = 0; i < chunks.size(); ++i) {
        auto future = std::async(std::launch::async, [this, &config, &chunks, i,
                                                     &total_downloaded, progress_cb, downloaded_size]() {
            const auto& chunk = chunks[i];
            size_t start_offset = chunk.start_offset + downloaded_size;
            size_t end_offset = chunk.end_offset + downloaded_size;
            
            auto write_callback = [this, i](const char* data, size_t size) -> size_t {
                if (should_cancel_) return 0;
                
                while (is_paused_ && !should_cancel_) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                
                if (should_cancel_) return 0;
                
                return file_manager_->writeChunk(data, size, i) ? size : 0;
            };
            
            auto progress_callback = [this, &total_downloaded, progress_cb]
                                   (size_t downloaded, size_t total) -> bool {
                if (should_cancel_) return false;
                
                total_downloaded += downloaded;
                updateStats(total_downloaded, current_stats_.total_size);
                notifyProgress(progress_cb);
                
                return !should_cancel_;
            };
            
            return http_client_->downloadRange(config.url, start_offset, end_offset,
                                             write_callback, progress_callback);
        });
        
        futures.push_back(std::move(future));
    }
    
    // Wait for all chunks to complete
    bool all_success = true;
    for (auto& future : futures) {
        if (!future.get()) {
            all_success = false;
        }
        
        if (should_cancel_) {
            all_success = false;
            break;
        }
    }
    
    return all_success && !should_cancel_;
}

bool Downloader::downloadSingleThreaded(const DownloadConfig& config,
                                        ProgressCallback progress_cb) {
    size_t resume_offset = current_stats_.downloaded_size;
    
    auto write_callback = [this](const char* data, size_t size) -> size_t {
        if (should_cancel_) return 0;
        
        while (is_paused_ && !should_cancel_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        if (should_cancel_) return 0;
        
        return file_manager_->writeData(data, size) ? size : 0;
    };
    
    auto progress_callback = [this, progress_cb](size_t downloaded, size_t total) -> bool {
        if (should_cancel_) return false;
        
        updateStats(downloaded, total);
        notifyProgress(progress_cb);
        
        return !should_cancel_;
    };
    
    // Setup range request if resuming
    HttpRequest request;
    request.url = config.url;
    request.timeout_seconds = config.timeout_seconds;
    request.user_agent = config.user_agent;
    
    if (resume_offset > 0) {
        request.use_range = true;
        request.range_start = resume_offset;
    }
    
    return http_client_->download(request, write_callback, progress_callback);
}

void Downloader::updateStats(size_t downloaded, size_t total) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    current_stats_.downloaded_size = downloaded;
    current_stats_.total_size = total;
    
    if (total > 0) {
        current_stats_.progress_percentage = 
            (static_cast<double>(downloaded) / static_cast<double>(total)) * 100.0;
    }
    
    current_stats_.download_speed = progress_tracker_->getDownloadSpeed();
    current_stats_.eta = progress_tracker_->getETA();
    
    progress_tracker_->updateProgress(downloaded, total);
}

void Downloader::notifyProgress(ProgressCallback callback) {
    if (callback) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        callback(current_stats_);
    }
}

bool Downloader::supportsRangeRequests(const std::string& url) {
    return http_client_->supportsRangeRequests(url);
}
