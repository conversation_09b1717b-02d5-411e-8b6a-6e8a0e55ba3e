#include "utils.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <regex>
#include <thread>
#include <ctime>
#include <iostream>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#else
#include <unistd.h>
#include <sys/stat.h>
#include <pwd.h>
#endif

namespace Utils {

std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::string toLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string toUpper(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::toupper);
    return result;
}

std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

bool startsWith(const std::string& str, const std::string& prefix) {
    return str.size() >= prefix.size() && 
           str.compare(0, prefix.size(), prefix) == 0;
}

bool endsWith(const std::string& str, const std::string& suffix) {
    return str.size() >= suffix.size() && 
           str.compare(str.size() - suffix.size(), suffix.size(), suffix) == 0;
}

std::string extractFileName(const std::string& url) {
    // Remove query parameters and fragments
    std::string clean_url = url;
    size_t query_pos = clean_url.find('?');
    if (query_pos != std::string::npos) {
        clean_url = clean_url.substr(0, query_pos);
    }
    
    size_t fragment_pos = clean_url.find('#');
    if (fragment_pos != std::string::npos) {
        clean_url = clean_url.substr(0, fragment_pos);
    }
    
    // Extract filename from path
    size_t last_slash = clean_url.find_last_of('/');
    if (last_slash != std::string::npos && last_slash < clean_url.length() - 1) {
        return clean_url.substr(last_slash + 1);
    }
    
    return "";
}

std::string extractDomain(const std::string& url) {
    std::regex url_regex(R"(^https?://([^/]+))");
    std::smatch match;
    
    if (std::regex_search(url, match, url_regex)) {
        return match[1].str();
    }
    
    return "";
}

bool isValidUrl(const std::string& url) {
    std::regex url_regex(R"(^https?://[^\s/$.?#].[^\s]*$)");
    return std::regex_match(url, url_regex);
}

std::string urlEncode(const std::string& str) {
    std::ostringstream encoded;
    encoded.fill('0');
    encoded << std::hex;
    
    for (char c : str) {
        if (std::isalnum(c) || c == '-' || c == '_' || c == '.' || c == '~') {
            encoded << c;
        } else {
            encoded << std::uppercase;
            encoded << '%' << std::setw(2) << static_cast<int>(static_cast<unsigned char>(c));
            encoded << std::nouppercase;
        }
    }
    
    return encoded.str();
}

std::string urlDecode(const std::string& str) {
    std::string decoded;
    
    for (size_t i = 0; i < str.length(); ++i) {
        if (str[i] == '%' && i + 2 < str.length()) {
            std::string hex = str.substr(i + 1, 2);
            char c = static_cast<char>(std::stoi(hex, nullptr, 16));
            decoded += c;
            i += 2;
        } else if (str[i] == '+') {
            decoded += ' ';
        } else {
            decoded += str[i];
        }
    }
    
    return decoded;
}

bool fileExists(const std::string& path) {
    return std::filesystem::exists(path);
}

bool directoryExists(const std::string& path) {
    return std::filesystem::exists(path) && std::filesystem::is_directory(path);
}

bool createDirectories(const std::string& path) {
    try {
        return std::filesystem::create_directories(path);
    } catch (const std::exception&) {
        return false;
    }
}

size_t getFileSize(const std::string& path) {
    try {
        return std::filesystem::file_size(path);
    } catch (const std::exception&) {
        return 0;
    }
}

std::string getFileExtension(const std::string& path) {
    std::filesystem::path p(path);
    return p.extension().string();
}

std::string getBaseName(const std::string& path) {
    std::filesystem::path p(path);
    return p.filename().string();
}

std::string getDirName(const std::string& path) {
    std::filesystem::path p(path);
    return p.parent_path().string();
}

std::string formatBytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    const size_t num_units = sizeof(units) / sizeof(units[0]);
    
    double size = static_cast<double>(bytes);
    size_t unit_index = 0;
    
    while (size >= 1024.0 && unit_index < num_units - 1) {
        size /= 1024.0;
        ++unit_index;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return oss.str();
}

std::string formatSpeed(double bytes_per_second) {
    return formatBytes(static_cast<size_t>(bytes_per_second)) + "/s";
}

std::string formatTime(std::chrono::seconds seconds) {
    auto total_seconds = seconds.count();
    
    if (total_seconds < 60) {
        return std::to_string(total_seconds) + "s";
    } else if (total_seconds < 3600) {
        auto minutes = total_seconds / 60;
        auto secs = total_seconds % 60;
        return std::to_string(minutes) + "m " + std::to_string(secs) + "s";
    } else {
        auto hours = total_seconds / 3600;
        auto minutes = (total_seconds % 3600) / 60;
        auto secs = total_seconds % 60;
        return std::to_string(hours) + "h " + std::to_string(minutes) + "m " + std::to_string(secs) + "s";
    }
}

std::string formatPercentage(double percentage) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(1) << percentage << "%";
    return oss.str();
}

std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

std::string getHomeDirectory() {
#ifdef _WIN32
    char path[MAX_PATH];
    if (SHGetFolderPathA(NULL, CSIDL_PROFILE, NULL, 0, path) == S_OK) {
        return std::string(path);
    }
    return "";
#else
    const char* home = getenv("HOME");
    if (home) {
        return std::string(home);
    }
    
    struct passwd* pw = getpwuid(getuid());
    if (pw) {
        return std::string(pw->pw_dir);
    }
    
    return "";
#endif
}

std::string getTempDirectory() {
    return std::filesystem::temp_directory_path().string();
}

int getNumberOfCores() {
    return std::thread::hardware_concurrency();
}

bool isValidFilePath(const std::string& path) {
    try {
        std::filesystem::path p(path);
        return !p.empty();
    } catch (const std::exception&) {
        return false;
    }
}

bool hasWritePermission(const std::string& path) {
    try {
        std::filesystem::path p(path);
        if (std::filesystem::exists(p)) {
            auto perms = std::filesystem::status(p).permissions();
            return (perms & std::filesystem::perms::owner_write) != std::filesystem::perms::none;
        } else {
            // Check parent directory
            auto parent = p.parent_path();
            if (std::filesystem::exists(parent)) {
                auto perms = std::filesystem::status(parent).permissions();
                return (perms & std::filesystem::perms::owner_write) != std::filesystem::perms::none;
            }
        }
    } catch (const std::exception&) {
        return false;
    }
    
    return false;
}

void clearLine() {
    std::cout << "\r\033[K";
}

void moveCursorUp(int lines) {
    std::cout << "\033[" << lines << "A";
}

void hideCursor() {
    std::cout << "\033[?25l";
}

void showCursor() {
    std::cout << "\033[?25h";
}

std::string createProgressBar(double percentage, int width, char fill, char empty) {
    int filled_width = static_cast<int>(percentage / 100.0 * width);
    
    std::string bar = "[";
    for (int i = 0; i < width; ++i) {
        if (i < filled_width) {
            bar += fill;
        } else {
            bar += empty;
        }
    }
    bar += "]";
    
    return bar;
}

} // namespace Utils
