#include "progress_tracker.h"
#include "utils.h"
#include <iostream>
#include <algorithm>

ProgressTracker::ProgressTracker() {
    reset();
}

ProgressTracker::~ProgressTracker() = default;

void ProgressTracker::updateProgress(size_t downloaded, size_t total) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    downloaded_size_ = downloaded;
    total_size_ = total;
    last_update_time_ = std::chrono::steady_clock::now();
    
    updateSpeedSamples();
}

void ProgressTracker::setTotalSize(size_t total) {
    std::lock_guard<std::mutex> lock(mutex_);
    total_size_ = total;
}

void ProgressTracker::addDownloaded(size_t bytes) {
    std::lock_guard<std::mutex> lock(mutex_);
    
    downloaded_size_ += bytes;
    last_update_time_ = std::chrono::steady_clock::now();
    
    updateSpeedSamples();
}

double ProgressTracker::getProgressPercentage() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (total_size_ == 0) {
        return 0.0;
    }
    
    return (static_cast<double>(downloaded_size_) / static_cast<double>(total_size_)) * 100.0;
}

double ProgressTracker::getDownloadSpeed() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return calculateSpeed();
}

std::string ProgressTracker::getETA() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (total_size_ == 0 || downloaded_size_ == 0 || is_paused_) {
        return "Unknown";
    }
    
    double speed = calculateSpeed();
    if (speed <= 0) {
        return "Unknown";
    }
    
    size_t remaining_bytes = total_size_ - downloaded_size_;
    auto remaining_seconds = std::chrono::seconds(static_cast<long>(remaining_bytes / speed));
    
    return Utils::formatTime(remaining_seconds);
}

size_t ProgressTracker::getTotalSize() const {
    return total_size_;
}

size_t ProgressTracker::getDownloadedSize() const {
    return downloaded_size_;
}

void ProgressTracker::start() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    start_time_ = std::chrono::steady_clock::now();
    last_update_time_ = start_time_;
    is_active_ = true;
    is_paused_ = false;
    
    speed_samples_.clear();
    speed_samples_.push_back({start_time_, downloaded_size_});
}

void ProgressTracker::pause() {
    std::lock_guard<std::mutex> lock(mutex_);
    is_paused_ = true;
}

void ProgressTracker::resume() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (is_paused_) {
        is_paused_ = false;
        last_update_time_ = std::chrono::steady_clock::now();
        
        // Add current state as new sample
        speed_samples_.push_back({last_update_time_, downloaded_size_});
    }
}

void ProgressTracker::reset() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    total_size_ = 0;
    downloaded_size_ = 0;
    is_active_ = false;
    is_paused_ = false;
    
    speed_samples_.clear();
}

std::string ProgressTracker::getProgressBar(int width) const {
    double percentage = getProgressPercentage();
    return Utils::createProgressBar(percentage, width);
}

std::string ProgressTracker::getFormattedStats() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    std::string stats;
    
    // Progress bar
    stats += getProgressBar(40) + " ";
    
    // Percentage
    stats += Utils::formatPercentage(getProgressPercentage()) + " ";
    
    // Downloaded / Total
    stats += Utils::formatBytes(downloaded_size_) + "/" + Utils::formatBytes(total_size_) + " ";
    
    // Speed
    double speed = calculateSpeed();
    if (speed > 0) {
        stats += Utils::formatSpeed(speed) + " ";
    }
    
    // ETA
    if (!is_paused_ && speed > 0) {
        stats += "ETA: " + getETA();
    } else if (is_paused_) {
        stats += "PAUSED";
    }
    
    return stats;
}

void ProgressTracker::printProgress() const {
    std::cout << "\r" << getFormattedStats() << std::flush;
}

void ProgressTracker::updateSpeedSamples() {
    auto now = std::chrono::steady_clock::now();
    
    // Add current sample
    speed_samples_.push_back({now, downloaded_size_});
    
    // Remove old samples outside the time window
    auto cutoff_time = now - SPEED_WINDOW;
    speed_samples_.erase(
        std::remove_if(speed_samples_.begin(), speed_samples_.end(),
                      [cutoff_time](const SpeedSample& sample) {
                          return sample.timestamp < cutoff_time;
                      }),
        speed_samples_.end()
    );
    
    // Limit number of samples
    if (speed_samples_.size() > MAX_SPEED_SAMPLES) {
        speed_samples_.erase(speed_samples_.begin(), 
                           speed_samples_.begin() + (speed_samples_.size() - MAX_SPEED_SAMPLES));
    }
}

double ProgressTracker::calculateSpeed() const {
    if (speed_samples_.size() < 2 || is_paused_) {
        return 0.0;
    }
    
    const auto& oldest = speed_samples_.front();
    const auto& newest = speed_samples_.back();
    
    auto time_diff = std::chrono::duration_cast<std::chrono::milliseconds>(
        newest.timestamp - oldest.timestamp).count();
    
    if (time_diff <= 0) {
        return 0.0;
    }
    
    size_t bytes_diff = newest.bytes - oldest.bytes;
    double seconds = static_cast<double>(time_diff) / 1000.0;
    
    return static_cast<double>(bytes_diff) / seconds;
}

std::string ProgressTracker::formatBytes(size_t bytes) const {
    return Utils::formatBytes(bytes);
}

std::string ProgressTracker::formatTime(std::chrono::seconds seconds) const {
    return Utils::formatTime(seconds);
}
