# 🎨 IDM Downloader GUI Options

This project now includes **3 different GUI options** to suit different preferences and requirements!

## 🚀 Quick Start

Choose your preferred GUI and run the corresponding build script:

```bash
# Option 1: Web-based GUI (Recommended - works everywhere!)
./build_web_gui.bat

# Option 2: Native Windows GUI (Lightweight, no dependencies)
./build_native_gui.bat

# Option 3: Qt-based GUI (Professional, cross-platform)
./build_gui.bat
```

---

## 🌐 Option 1: Web-based GUI (Recommended)

**Best for: Universal compatibility, modern interface**

### Features:
- ✅ **Works on any device** with a web browser
- ✅ **No additional dependencies** required
- ✅ **Modern, responsive design**
- ✅ **Real-time progress updates**
- ✅ **Mobile-friendly interface**

### How to use:
```bash
# Build
./build_web_gui.bat

# Run
./IDMDownloaderWebGUI.exe

# Open browser to: http://localhost:8080
```

### Screenshots:
- Beautiful gradient design
- Real-time progress bars
- Mobile-responsive layout
- Professional statistics display

---

## 🖥️ Option 2: Native Windows GUI

**Best for: Windows users who want lightweight, native experience**

### Features:
- ✅ **Pure Windows API** - no external dependencies
- ✅ **Native Windows look and feel**
- ✅ **Extremely lightweight** (~2MB executable)
- ✅ **Fast startup time**
- ✅ **Integrated with Windows dialogs**

### How to use:
```bash
# Build
./build_native_gui.bat

# Run
./IDMDownloaderNativeGUI.exe
```

### Interface Elements:
- Native Windows controls
- File browser integration
- Progress bars with Windows styling
- Standard Windows menus and dialogs

---

## 🎯 Option 3: Qt-based GUI (Professional)

**Best for: Cross-platform, feature-rich interface**

### Features:
- ✅ **Cross-platform** (Windows, Linux, macOS)
- ✅ **Professional appearance**
- ✅ **Advanced features** (tabs, queues, logs)
- ✅ **Customizable themes**
- ✅ **Rich widgets and controls**

### Requirements:
- Qt6 development libraries
- Additional ~50MB for Qt runtime

### How to use:
```bash
# Install Qt6 first (if not already installed)
pacman -S mingw-w64-x86_64-qt6

# Build
./build_gui.bat

# Run
./gui_build/IDMDownloaderGUI.exe
```

---

## 📊 Comparison Table

| Feature | Web GUI | Native GUI | Qt GUI |
|---------|---------|------------|--------|
| **Dependencies** | None | None | Qt6 |
| **File Size** | ~3MB | ~2MB | ~50MB+ |
| **Startup Time** | Fast | Fastest | Medium |
| **Cross-platform** | ✅ | ❌ (Windows only) | ✅ |
| **Mobile Support** | ✅ | ❌ | ❌ |
| **Native Look** | ❌ | ✅ | ⚠️ |
| **Advanced Features** | ⚠️ | ⚠️ | ✅ |
| **Ease of Build** | ✅ | ✅ | ⚠️ |

---

## 🎨 Interface Previews

### Web GUI Features:
```
🌐 Modern Web Interface
├── 📱 Responsive design
├── 🎨 Beautiful gradients
├── 📊 Real-time progress
├── ⚡ Fast updates
└── 🔧 Easy configuration
```

### Native GUI Features:
```
🖥️ Windows Native Interface
├── 📁 File browser integration
├── 🎯 Native controls
├── ⚡ Lightning fast
├── 💾 Minimal memory usage
└── 🔗 Windows integration
```

### Qt GUI Features:
```
🎯 Professional Qt Interface
├── 📑 Tabbed interface
├── 📋 Download queue
├── 📝 Detailed logs
├── ⚙️ Advanced settings
└── 🎨 Custom themes
```

---

## 🛠️ Build Requirements

### All GUIs:
- GCC with C++17 support
- libcurl development libraries
- Windows Sockets library (ws2_32)

### Additional for Qt GUI:
- Qt6 development libraries
- CMake 3.16+

### Additional for Web GUI:
- None! (Uses built-in HTTP server)

---

## 🚀 Recommended Usage

1. **For most users**: Start with the **Web GUI** - it's the easiest to build and works everywhere
2. **For Windows power users**: Try the **Native GUI** for maximum performance
3. **For developers**: Use the **Qt GUI** for the most features and customization

---

## 🎯 Next Steps

After building your preferred GUI:

1. **Test with small files** first
2. **Configure your download settings**
3. **Try the multi-threaded downloads**
4. **Explore the progress tracking**
5. **Enjoy fast downloads!** 🚀

---

## 🤝 Contributing

Want to improve the GUIs? Here are some ideas:

- 🎨 **Web GUI**: Add dark mode, more themes
- 🖥️ **Native GUI**: Add more Windows integration
- 🎯 **Qt GUI**: Implement advanced queue management
- 📱 **Mobile**: Create a mobile app version

---

## 📞 Support

If you encounter issues:

1. Check the build output for error messages
2. Ensure all dependencies are installed
3. Try the simpler Web GUI first
4. Check the main README.md for troubleshooting

**Happy downloading!** 🎉
