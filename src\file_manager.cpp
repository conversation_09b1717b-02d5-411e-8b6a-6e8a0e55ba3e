#include "file_manager.h"
#include "utils.h"
#include <filesystem>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <iostream>

FileManager::FileManager() 
    : current_size_(0), expected_size_(0), is_open_(false), is_chunked_(false) {
}

FileManager::~FileManager() {
    closeFile();
}

bool FileManager::createFile(const std::string& file_path, size_t expected_size) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    file_path_ = file_path;
    expected_size_ = expected_size;
    current_size_ = 0;
    is_chunked_ = false;
    
    // Create directory if it doesn't exist
    std::filesystem::path path(file_path);
    auto parent_dir = path.parent_path();
    if (!parent_dir.empty() && !std::filesystem::exists(parent_dir)) {
        if (!std::filesystem::create_directories(parent_dir)) {
            return false;
        }
    }
    
    // Create the file
    file_stream_ = std::make_unique<std::fstream>(file_path, 
        std::ios::out | std::ios::binary | std::ios::trunc);
    
    if (!file_stream_->is_open()) {
        return false;
    }
    
    // Pre-allocate space if expected size is known
    if (expected_size > 0) {
        try {
            file_stream_->seekp(expected_size - 1);
            file_stream_->write("", 1);
            file_stream_->seekp(0);
        } catch (const std::exception&) {
            // Pre-allocation failed, continue without it
        }
    }
    
    is_open_ = true;
    resume_info_path_ = getResumeInfoPath(file_path);
    
    return true;
}

bool FileManager::openFile(const std::string& file_path, bool resume) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    file_path_ = file_path;
    resume_info_path_ = getResumeInfoPath(file_path);
    is_chunked_ = false;
    
    if (resume && canResume(file_path)) {
        // Open for append
        file_stream_ = std::make_unique<std::fstream>(file_path, 
            std::ios::out | std::ios::binary | std::ios::app);
        
        if (file_stream_->is_open()) {
            current_size_ = getExistingFileSize(file_path);
            loadResumeInfo(file_path);
            is_open_ = true;
            return true;
        }
    }
    
    // Create new file
    return createFile(file_path, expected_size_);
}

bool FileManager::writeData(const char* data, size_t size, size_t offset) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!is_open_ || !file_stream_) {
        return false;
    }
    
    try {
        if (offset > 0) {
            file_stream_->seekp(offset);
        }
        
        file_stream_->write(data, size);
        file_stream_->flush();
        
        if (offset == 0 || offset == current_size_) {
            current_size_ += size;
        } else {
            // Update current size if writing beyond current end
            auto current_pos = file_stream_->tellp();
            if (static_cast<size_t>(current_pos) > current_size_) {
                current_size_ = static_cast<size_t>(current_pos);
            }
        }
        
        return file_stream_->good();
    } catch (const std::exception&) {
        return false;
    }
}

bool FileManager::writeChunk(const char* data, size_t size, size_t chunk_id) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (!is_chunked_ || chunk_id >= chunks_.size()) {
        return false;
    }
    
    auto& chunk = chunks_[chunk_id];
    
    // Open chunk file if not already open
    if (chunk_id >= chunk_streams_.size() || !chunk_streams_[chunk_id]) {
        if (chunk_id >= chunk_streams_.size()) {
            chunk_streams_.resize(chunk_id + 1);
        }
        
        chunk_streams_[chunk_id] = std::make_unique<std::fstream>(
            chunk.temp_file_path, std::ios::out | std::ios::binary | std::ios::app);
        
        if (!chunk_streams_[chunk_id]->is_open()) {
            return false;
        }
    }
    
    try {
        chunk_streams_[chunk_id]->write(data, size);
        chunk_streams_[chunk_id]->flush();
        
        chunk.current_offset += size;
        
        // Check if chunk is complete
        if (chunk.current_offset >= chunk.end_offset - chunk.start_offset + 1) {
            chunk.is_complete = true;
            chunk_streams_[chunk_id]->close();
        }
        
        return true;
    } catch (const std::exception&) {
        return false;
    }
}

bool FileManager::finalizeFile() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (is_chunked_) {
        return mergeChunks(file_path_);
    }
    
    if (file_stream_) {
        file_stream_->close();
    }
    
    // Delete resume info
    deleteResumeInfo(file_path_);
    
    return true;
}

void FileManager::closeFile() {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (file_stream_) {
        file_stream_->close();
        file_stream_.reset();
    }
    
    // Close chunk streams
    for (auto& stream : chunk_streams_) {
        if (stream && stream->is_open()) {
            stream->close();
        }
    }
    chunk_streams_.clear();
    
    is_open_ = false;
}

bool FileManager::canResume(const std::string& file_path) const {
    return Utils::fileExists(file_path) && Utils::fileExists(getResumeInfoPath(file_path));
}

size_t FileManager::getExistingFileSize(const std::string& file_path) const {
    return Utils::getFileSize(file_path);
}

bool FileManager::createResumeInfo(const std::string& file_path, size_t total_size) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    expected_size_ = total_size;
    return writeResumeInfo();
}

bool FileManager::loadResumeInfo(const std::string& file_path) {
    std::string resume_path = getResumeInfoPath(file_path);
    
    if (!Utils::fileExists(resume_path)) {
        return false;
    }
    
    std::ifstream file(resume_path);
    if (!file.is_open()) {
        return false;
    }
    
    std::string line;
    if (std::getline(file, line)) {
        try {
            expected_size_ = std::stoull(line);
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }
    
    return false;
}

bool FileManager::deleteResumeInfo(const std::string& file_path) {
    std::string resume_path = getResumeInfoPath(file_path);
    
    if (Utils::fileExists(resume_path)) {
        try {
            return std::filesystem::remove(resume_path);
        } catch (const std::exception&) {
            return false;
        }
    }
    
    return true;
}

bool FileManager::setupChunks(size_t total_size, int num_chunks) {
    std::lock_guard<std::mutex> lock(file_mutex_);
    
    if (num_chunks <= 1) {
        is_chunked_ = false;
        return true;
    }
    
    chunks_.clear();
    chunk_streams_.clear();
    
    size_t chunk_size = total_size / num_chunks;
    size_t remaining = total_size % num_chunks;
    
    size_t current_offset = 0;
    
    for (int i = 0; i < num_chunks; ++i) {
        FileChunk chunk;
        chunk.start_offset = current_offset;
        chunk.end_offset = current_offset + chunk_size - 1;
        
        // Add remaining bytes to the last chunk
        if (i == num_chunks - 1) {
            chunk.end_offset += remaining;
        }
        
        chunk.current_offset = 0;
        chunk.is_complete = false;
        chunk.temp_file_path = generateTempFileName(i);
        
        chunks_.push_back(chunk);
        current_offset = chunk.end_offset + 1;
    }
    
    is_chunked_ = true;
    expected_size_ = total_size;
    
    return true;
}

std::vector<FileChunk> FileManager::getChunks() const {
    std::lock_guard<std::mutex> lock(file_mutex_);
    return chunks_;
}

bool FileManager::mergeChunks(const std::string& output_path) {
    // Close all chunk streams first
    for (auto& stream : chunk_streams_) {
        if (stream && stream->is_open()) {
            stream->close();
        }
    }
    
    // Create output file
    std::ofstream output(output_path, std::ios::binary | std::ios::trunc);
    if (!output.is_open()) {
        return false;
    }
    
    // Merge chunks in order
    for (const auto& chunk : chunks_) {
        std::ifstream chunk_file(chunk.temp_file_path, std::ios::binary);
        if (!chunk_file.is_open()) {
            output.close();
            return false;
        }
        
        // Copy chunk data to output file
        output << chunk_file.rdbuf();
        chunk_file.close();
        
        // Delete temporary chunk file
        try {
            std::filesystem::remove(chunk.temp_file_path);
        } catch (const std::exception&) {
            // Continue even if deletion fails
        }
    }
    
    output.close();
    
    // Delete resume info
    deleteResumeInfo(output_path);
    
    return true;
}

bool FileManager::fileExists(const std::string& file_path) const {
    return Utils::fileExists(file_path);
}

bool FileManager::createDirectory(const std::string& dir_path) const {
    return Utils::createDirectories(dir_path);
}

std::string FileManager::getFileName(const std::string& url) const {
    return Utils::extractFileName(url);
}

std::string FileManager::sanitizeFileName(const std::string& filename) const {
    std::string sanitized = filename;

    // Replace invalid characters with underscores
    const std::string invalid_chars = "<>:\"/\\|?*";
    for (char c : invalid_chars) {
        std::replace(sanitized.begin(), sanitized.end(), c, '_');
    }

    // Remove leading/trailing spaces and dots
    sanitized = Utils::trim(sanitized);
    while (!sanitized.empty() && sanitized.back() == '.') {
        sanitized.pop_back();
    }

    // Ensure filename is not empty
    if (sanitized.empty()) {
        sanitized = "download";
    }

    return sanitized;
}

bool FileManager::hasWritePermission(const std::string& dir_path) const {
    return Utils::hasWritePermission(dir_path);
}

size_t FileManager::getCurrentSize() const {
    return current_size_;
}

std::string FileManager::getFilePath() const {
    return file_path_;
}

bool FileManager::isOpen() const {
    return is_open_;
}

std::string FileManager::getResumeInfoPath(const std::string& file_path) const {
    return file_path + ".idm_resume";
}

bool FileManager::writeResumeInfo() const {
    if (resume_info_path_.empty()) {
        return false;
    }

    std::ofstream file(resume_info_path_);
    if (!file.is_open()) {
        return false;
    }

    file << expected_size_ << std::endl;
    file << current_size_ << std::endl;
    file << Utils::getCurrentTimestamp() << std::endl;

    return file.good();
}

std::string FileManager::generateTempFileName(size_t chunk_id) const {
    std::string temp_dir = Utils::getTempDirectory();
    std::string base_name = Utils::getBaseName(file_path_);

    std::ostringstream oss;
    oss << temp_dir << "/idm_chunk_" << chunk_id << "_" << base_name;

    return oss.str();
}
