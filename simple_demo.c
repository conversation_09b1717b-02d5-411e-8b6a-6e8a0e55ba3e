/*
 * Simple IDM Downloader Demo - Pure C version
 * This works with just a basic C compiler (gcc, clang, or even tcc)
 * Compile with: gcc simple_demo.c -o demo.exe
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#ifdef _WIN32
#include <windows.h>
#define SLEEP_MS(x) Sleep(x)
#else
#include <unistd.h>
#define SLEEP_MS(x) usleep((x)*1000)
#endif

void print_header() {
    printf("\n");
    printf("  ██╗██████╗ ███╗   ███╗    ██████╗  ██████╗ ██╗    ██╗███╗   ██╗██╗      ██████╗  █████╗ ██████╗ ███████╗██████╗ \n");
    printf("  ██║██╔══██╗████╗ ████║    ██╔══██╗██╔═══██╗██║    ██║████╗  ██║██║     ██╔═══██╗██╔══██╗██╔══██╗██╔════╝██╔══██╗\n");
    printf("  ██║██║  ██║██╔████╔██║    ██║  ██║██║   ██║██║ █╗ ██║██╔██╗ ██║██║     ██║   ██║███████║██║  ██║█████╗  ██████╔╝\n");
    printf("  ██║██║  ██║██║╚██╔╝██║    ██║  ██║██║   ██║██║███╗██║██║╚██╗██║██║     ██║   ██║██╔══██║██║  ██║██╔══╝  ██╔══██╗\n");
    printf("  ██║██████╔╝██║ ╚═╝ ██║    ██████╔╝╚██████╔╝╚███╔███╔╝██║ ╚████║███████╗╚██████╔╝██║  ██║██████╔╝███████╗██║  ██║\n");
    printf("  ╚═╝╚═════╝ ╚═╝     ╚═╝    ╚═════╝  ╚═════╝  ╚══╝╚══╝ ╚═╝  ╚═══╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═════╝ ╚══════╝╚═╝  ╚═╝\n");
    printf("\n");
    printf("                                    🚀 C++ Multi-threaded Downloader 🚀\n");
    printf("                                         Version 1.0 - Demo Mode\n\n");
}

void print_features() {
    printf("🎯 Key Features:\n");
    printf("   ✅ Multi-threaded downloads (up to 16 connections)\n");
    printf("   ✅ Resume interrupted downloads\n");
    printf("   ✅ Real-time progress tracking\n");
    printf("   ✅ HTTP/HTTPS support with SSL\n");
    printf("   ✅ Smart chunk management\n");
    printf("   ✅ Cross-platform compatibility\n");
    printf("   ✅ Command-line interface\n\n");
    
    printf("📋 Usage Examples:\n");
    printf("   Basic:     IDMDownloader https://example.com/file.zip\n");
    printf("   Fast:      IDMDownloader -c 8 https://example.com/file.zip\n");
    printf("   Resume:    IDMDownloader --resume https://example.com/file.zip\n");
    printf("   Custom:    IDMDownloader -o myfile.zip -c 4 https://example.com/file.zip\n\n");
}

const char* format_bytes(long bytes) {
    static char buffer[32];
    if (bytes < 1024) {
        sprintf(buffer, "%ld B", bytes);
    } else if (bytes < 1024 * 1024) {
        sprintf(buffer, "%.1f KB", (double)bytes / 1024.0);
    } else {
        sprintf(buffer, "%.1f MB", (double)bytes / (1024.0 * 1024.0));
    }
    return buffer;
}

void simulate_download() {
    printf("🌐 Simulating download: https://example.com/demo-file.zip\n");
    printf("📁 Output file: demo_download.zip\n");
    printf("🔗 Connections: 4\n\n");
    
    // Create actual file
    FILE* file = fopen("demo_download.zip", "wb");
    if (!file) {
        printf("❌ Error: Cannot create demo file\n");
        return;
    }
    
    printf("🔍 Detecting file size... ");
    fflush(stdout);
    SLEEP_MS(500);
    printf("5.2 MB\n");
    
    printf("🔗 Testing range requests... ");
    fflush(stdout);
    SLEEP_MS(300);
    printf("Supported\n");
    
    printf("⚡ Starting multi-threaded download...\n\n");
    
    const int total_steps = 50;
    const long file_size = 5242880; /* 5MB */
    int i;
    
    for (i = 0; i <= total_steps; i++) {
        double percent = (double)i / total_steps * 100.0;
        long downloaded = (long)(file_size * percent / 100.0);
        int j;
        
        /* Write some data to file */
        if (i < total_steps) {
            char data[1024];
            int chunk_size = file_size / total_steps;
            int k;
            for (k = 0; k < chunk_size && k < 1024; k++) {
                data[k] = 'X';
            }
            if (chunk_size > 1024) {
                for (k = 0; k < chunk_size / 1024; k++) {
                    fwrite(data, 1, 1024, file);
                }
                fwrite(data, 1, chunk_size % 1024, file);
            } else {
                fwrite(data, 1, chunk_size, file);
            }
        }
        
        /* Show progress */
        printf("\r[");
        const int bar_width = 40;
        int filled = (int)(percent / 100.0 * bar_width);
        
        for (j = 0; j < bar_width; j++) {
            if (j < filled) printf("█");
            else if (j == filled) printf("▓");
            else printf("░");
        }
        
        printf("] %.1f%% %s/%s %.1f MB/s", 
               percent, 
               format_bytes(downloaded),
               format_bytes(file_size),
               (double)(downloaded) / 1024.0 / 1024.0 / (i + 1) * 10);
        
        fflush(stdout);
        SLEEP_MS(50 + (i % 3) * 10);
    }
    
    fclose(file);
    
    printf("\n\n✅ Download completed successfully!\n");
    printf("📁 File saved: demo_download.zip\n");
    printf("📊 Size: 5.2 MB\n");
    printf("⏱️  Time: 3.2 seconds\n");
    printf("📈 Average speed: 1.6 MB/s\n\n");
    
    printf("🎉 This was a demonstration!\n");
    printf("📝 The real downloader will:\n");
    printf("   • Actually download from HTTP/HTTPS URLs\n");
    printf("   • Support all major protocols\n");
    printf("   • Handle errors and retries\n");
    printf("   • Resume partial downloads\n");
    printf("   • Work with any file size\n\n");
}

void print_build_instructions() {
    printf("🛠️  To build the full version:\n");
    printf("   1. Install dependencies:\n");
    printf("      pacman -S mingw-w64-x86_64-gcc\n");
    printf("      pacman -S mingw-w64-x86_64-cmake\n");
    printf("      pacman -S mingw-w64-x86_64-curl\n\n");
    printf("   2. Build with CMake:\n");
    printf("      mkdir build && cd build\n");
    printf("      cmake ..\n");
    printf("      make\n\n");
    printf("   3. Or build with Make:\n");
    printf("      make\n\n");
    printf("   4. Run:\n");
    printf("      ./IDMDownloader <URL>\n\n");
}

int main(int argc, char* argv[]) {
    if (argc > 1) {
        if (strcmp(argv[1], "--help") == 0 || strcmp(argv[1], "-h") == 0) {
            printf("IDM Downloader Demo\n\n");
            printf("Usage: %s [--help]\n\n", argv[0]);
            printf("This is a demonstration of the IDM Downloader interface.\n");
            printf("Run without arguments to see the demo.\n\n");
            return 0;
        }
    }
    
    print_header();
    print_features();
    simulate_download();
    print_build_instructions();
    
    return 0;
}
