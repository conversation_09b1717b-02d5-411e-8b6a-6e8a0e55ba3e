#pragma once

#include <string>
#include <vector>
#include <chrono>

namespace Utils {
    // String utilities
    std::string trim(const std::string& str);
    std::string toLower(const std::string& str);
    std::string toUpper(const std::string& str);
    std::vector<std::string> split(const std::string& str, char delimiter);
    bool startsWith(const std::string& str, const std::string& prefix);
    bool endsWith(const std::string& str, const std::string& suffix);
    
    // URL utilities
    std::string extractFileName(const std::string& url);
    std::string extractDomain(const std::string& url);
    bool isValidUrl(const std::string& url);
    std::string urlEncode(const std::string& str);
    std::string urlDecode(const std::string& str);
    
    // File utilities
    bool fileExists(const std::string& path);
    bool directoryExists(const std::string& path);
    bool createDirectories(const std::string& path);
    size_t getFileSize(const std::string& path);
    std::string getFileExtension(const std::string& path);
    std::string getBaseName(const std::string& path);
    std::string getDirName(const std::string& path);
    
    // Format utilities
    std::string formatBytes(size_t bytes);
    std::string formatSpeed(double bytes_per_second);
    std::string formatTime(std::chrono::seconds seconds);
    std::string formatPercentage(double percentage);
    
    // System utilities
    std::string getCurrentTimestamp();
    std::string getHomeDirectory();
    std::string getTempDirectory();
    int getNumberOfCores();
    
    // Validation utilities
    bool isValidFilePath(const std::string& path);
    bool hasWritePermission(const std::string& path);
    
    // Console utilities
    void clearLine();
    void moveCursorUp(int lines = 1);
    void hideCursor();
    void showCursor();
    
    // Progress bar
    std::string createProgressBar(double percentage, int width = 50, 
                                 char fill = '=', char empty = ' ');
}
