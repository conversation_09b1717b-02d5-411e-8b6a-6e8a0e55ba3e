@echo off
REM IDM Downloader Build Script for Windows
REM This script builds the IDM Downloader project on Windows

setlocal enabledelayedexpansion

REM Default values
set BUILD_TYPE=Release
set CLEAN_BUILD=false
set INSTALL=false
set JOBS=%NUMBER_OF_PROCESSORS%
set VCPKG_ROOT=C:\vcpkg

REM Parse command line arguments
:parse_args
if "%~1"=="" goto end_parse
if "%~1"=="-d" (
    set BUILD_TYPE=Debug
    shift
    goto parse_args
)
if "%~1"=="--debug" (
    set BUILD_TYPE=Debug
    shift
    goto parse_args
)
if "%~1"=="-c" (
    set CLEAN_BUILD=true
    shift
    goto parse_args
)
if "%~1"=="--clean" (
    set CLEAN_BUILD=true
    shift
    goto parse_args
)
if "%~1"=="-i" (
    set INSTALL=true
    shift
    goto parse_args
)
if "%~1"=="--install" (
    set INSTALL=true
    shift
    goto parse_args
)
if "%~1"=="-j" (
    set JOBS=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="--jobs" (
    set JOBS=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-h" goto show_help
if "%~1"=="--help" goto show_help
if "%~1"=="--vcpkg" (
    set VCPKG_ROOT=%~2
    shift
    shift
    goto parse_args
)

echo [ERROR] Unknown option: %~1
echo Use -h or --help for usage information.
exit /b 1

:show_help
echo IDM Downloader Build Script for Windows
echo.
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -d, --debug         Build in debug mode (default: Release)
echo   -c, --clean         Clean build directory before building
echo   -i, --install       Install after building
echo   -j, --jobs N        Number of parallel jobs (default: %NUMBER_OF_PROCESSORS%)
echo   --vcpkg PATH        Path to vcpkg installation (default: C:\vcpkg)
echo   -h, --help          Show this help message
echo.
echo Examples:
echo   %0                      # Build in release mode
echo   %0 --debug             # Build in debug mode
echo   %0 --clean --install   # Clean build and install
echo   %0 -j 8                # Build with 8 parallel jobs
echo   %0 --vcpkg D:\vcpkg    # Use custom vcpkg path
exit /b 0

:end_parse

echo [INFO] Starting IDM Downloader build process...
echo [INFO] Build type: %BUILD_TYPE%
echo [INFO] Parallel jobs: %JOBS%
echo [INFO] vcpkg root: %VCPKG_ROOT%

REM Check if we're in the right directory
if not exist "CMakeLists.txt" (
    echo [ERROR] CMakeLists.txt not found. Please run this script from the project root directory.
    exit /b 1
)

REM Check for required tools
echo [INFO] Checking dependencies...

REM Check for CMake
cmake --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] CMake is not installed or not in PATH.
    echo Please install CMake from https://cmake.org/download/
    exit /b 1
)

for /f "tokens=3" %%i in ('cmake --version ^| findstr /C:"cmake version"') do set CMAKE_VERSION=%%i
echo [INFO] Found CMake version: %CMAKE_VERSION%

REM Check for Visual Studio
where cl >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Visual Studio compiler (cl.exe) not found in PATH.
    echo Please run this script from a Visual Studio Developer Command Prompt.
    echo Or make sure Visual Studio 2017 or later is installed.
)

REM Check for vcpkg
if exist "%VCPKG_ROOT%\vcpkg.exe" (
    echo [INFO] Found vcpkg at: %VCPKG_ROOT%
) else (
    echo [WARNING] vcpkg not found at: %VCPKG_ROOT%
    echo Please install vcpkg and libcurl:
    echo   git clone https://github.com/Microsoft/vcpkg.git
    echo   cd vcpkg
    echo   .\bootstrap-vcpkg.bat
    echo   .\vcpkg install curl:x64-windows
)

REM Create build directory
set BUILD_DIR=build

if "%CLEAN_BUILD%"=="true" (
    echo [INFO] Cleaning build directory...
    if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
)

if not exist "%BUILD_DIR%" (
    echo [INFO] Creating build directory...
    mkdir "%BUILD_DIR%"
)

cd "%BUILD_DIR%"

REM Configure with CMake
echo [INFO] Configuring project with CMake...

set CMAKE_TOOLCHAIN_FILE=%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake

if exist "%CMAKE_TOOLCHAIN_FILE%" (
    cmake -DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DCMAKE_TOOLCHAIN_FILE="%CMAKE_TOOLCHAIN_FILE%" ..
) else (
    echo [WARNING] vcpkg toolchain file not found, building without vcpkg
    cmake -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ..
)

if errorlevel 1 (
    echo [ERROR] CMake configuration failed!
    exit /b 1
)

REM Build the project
echo [INFO] Building project...
cmake --build . --config %BUILD_TYPE% --parallel %JOBS%

if errorlevel 1 (
    echo [ERROR] Build failed!
    exit /b 1
)

echo [SUCCESS] Build completed successfully!

REM Check if executable was created
set EXECUTABLE=IDMDownloader.exe
if "%BUILD_TYPE%"=="Debug" set EXECUTABLE=Debug\IDMDownloader.exe
if "%BUILD_TYPE%"=="Release" set EXECUTABLE=Release\IDMDownloader.exe

if exist "%EXECUTABLE%" (
    echo [SUCCESS] Executable created: %BUILD_DIR%\%EXECUTABLE%
    
    REM Test if executable runs
    "%EXECUTABLE%" --help >nul 2>&1
    if errorlevel 1 (
        echo [WARNING] Executable test failed - there might be missing dependencies
    ) else (
        echo [SUCCESS] Executable test passed!
    )
) else (
    echo [ERROR] Executable not found after build!
    echo Expected location: %BUILD_DIR%\%EXECUTABLE%
    exit /b 1
)

REM Install if requested
if "%INSTALL%"=="true" (
    echo [INFO] Installing...
    cmake --build . --config %BUILD_TYPE% --target install
    
    if errorlevel 1 (
        echo [ERROR] Installation failed!
        exit /b 1
    ) else (
        echo [SUCCESS] Installation completed!
    )
)

REM Final instructions
echo.
echo [SUCCESS] Build process completed!
echo.
echo [INFO] To run the downloader:
echo   cd %BUILD_DIR% ^&^& %EXECUTABLE% --help
echo.
echo [INFO] Example usage:
echo   %EXECUTABLE% https://example.com/file.zip
echo   %EXECUTABLE% -c 8 -o myfile.zip https://example.com/file.zip
echo.

if "%INSTALL%"=="true" (
    echo [INFO] Since you installed the program, you can also run it from anywhere:
    echo   IDMDownloader https://example.com/file.zip
)

cd ..
pause
