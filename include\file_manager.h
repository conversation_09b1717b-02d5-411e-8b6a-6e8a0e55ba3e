#pragma once

#include <string>
#include <fstream>
#include <vector>
#include <memory>
#include <mutex>

struct FileChunk {
    size_t start_offset;
    size_t end_offset;
    size_t current_offset;
    bool is_complete;
    std::string temp_file_path;
};

class FileManager {
public:
    FileManager();
    ~FileManager();

    // File operations
    bool createFile(const std::string& file_path, size_t expected_size = 0);
    bool openFile(const std::string& file_path, bool resume = true);
    bool writeData(const char* data, size_t size, size_t offset = 0);
    bool writeChunk(const char* data, size_t size, size_t chunk_id);
    bool finalizeFile();
    void closeFile();
    
    // Resume functionality
    bool canResume(const std::string& file_path) const;
    size_t getExistingFileSize(const std::string& file_path) const;
    bool createResumeInfo(const std::string& file_path, size_t total_size);
    bool loadResumeInfo(const std::string& file_path);
    bool deleteResumeInfo(const std::string& file_path);
    
    // Multi-part download support
    bool setupChunks(size_t total_size, int num_chunks);
    std::vector<FileChunk> getChunks() const;
    bool mergeChunks(const std::string& output_path);
    
    // Utility functions
    bool fileExists(const std::string& file_path) const;
    bool createDirectory(const std::string& dir_path) const;
    std::string getFileName(const std::string& url) const;
    std::string sanitizeFileName(const std::string& filename) const;
    bool hasWritePermission(const std::string& dir_path) const;
    
    // Getters
    size_t getCurrentSize() const;
    std::string getFilePath() const;
    bool isOpen() const;

private:
    std::string file_path_;
    std::string resume_info_path_;
    std::unique_ptr<std::fstream> file_stream_;
    std::vector<FileChunk> chunks_;
    std::vector<std::unique_ptr<std::fstream>> chunk_streams_;
    
    mutable std::mutex file_mutex_;
    size_t current_size_;
    size_t expected_size_;
    bool is_open_;
    bool is_chunked_;
    
    // Helper functions
    std::string getResumeInfoPath(const std::string& file_path) const;
    bool writeResumeInfo() const;
    std::string generateTempFileName(size_t chunk_id) const;
};
