@echo off
echo Building IDM Downloader Web GUI...

REM Build the web GUI version (simpler, no Qt required)
echo Compiling web server...

g++ -std=c++17 -I include ^
    web_gui/web_server.cpp ^
    src/downloader.cpp ^
    src/http_client.cpp ^
    src/file_manager.cpp ^
    src/progress_tracker.cpp ^
    src/utils.cpp ^
    -lcurl -lws2_32 -lwldap32 ^
    -o IDMDownloaderWebGUI.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Web GUI build successful!
    echo.
    echo 🌐 To run the Web GUI:
    echo   1. Run: IDMDownloaderWebGUI.exe
    echo   2. Open your browser to: http://localhost:8080
    echo   3. Use the web interface to download files
    echo.
    echo 📱 The web interface works on any device with a browser!
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo Make sure you have:
    echo   - GCC with C++17 support
    echo   - libcurl development libraries
    echo   - Windows Sockets library
)

pause
