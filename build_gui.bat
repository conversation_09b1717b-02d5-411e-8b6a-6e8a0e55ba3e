@echo off
echo Building IDM Downloader GUI...

REM Create build directory for GUI
if not exist "gui_build" mkdir gui_build
cd gui_build

REM Configure with CMake
echo Configuring with CMake...
cmake -G "MinGW Makefiles" ../gui

REM Build the project
echo Building...
cmake --build .

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Build successful!
    echo.
    echo GUI executable created: gui_build/IDMDownloaderGUI.exe
    echo.
    echo To run the GUI:
    echo   cd gui_build
    echo   ./IDMDownloaderGUI.exe
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo Check the error messages above.
)

pause
