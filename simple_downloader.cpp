/*
 * Simple IDM Downloader - Single File Version
 * This is a simplified version that can be compiled with minimal dependencies
 * Compile with: g++ -std=c++17 simple_downloader.cpp -lcurl -o downloader
 */

#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <chrono>
#include <iomanip>
#include <sstream>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// Mock curl for demonstration (replace with actual libcurl in real implementation)
#ifndef MOCK_CURL
#include <curl/curl.h>
#else
// Mock definitions for demonstration
typedef void CURL;
typedef int CURLcode;
#define CURLE_OK 0
#define CURLOPT_URL 1
#define CURLOPT_WRITEFUNCTION 2
#define CURLOPT_WRITEDATA 3
#define CURLOPT_PROGRESSFUNCTION 4
#define CURLOPT_PROGRESSDATA 5
#define CURLOPT_NOPROGRESS 6
#define CURLOPT_FOLLOWLOCATION 7
#define CURLOPT_USERAGENT 8

CURL* curl_easy_init() { return nullptr; }
void curl_easy_cleanup(CURL*) {}
CURLcode curl_easy_setopt(CURL*, int, ...) { return CURLE_OK; }
CURLcode curl_easy_perform(CURL*) { return CURLE_OK; }
void curl_global_init(long) {}
void curl_global_cleanup() {}
#endif

class SimpleDownloader {
private:
    struct ProgressInfo {
        size_t total_size = 0;
        size_t downloaded_size = 0;
        std::chrono::steady_clock::time_point start_time;
        bool show_progress = true;
    };

    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, void* userp) {
        size_t realsize = size * nmemb;
        std::ofstream* file = static_cast<std::ofstream*>(userp);
        
        if (file && file->is_open()) {
            file->write(static_cast<const char*>(contents), realsize);
            return realsize;
        }
        return 0;
    }

    static int ProgressCallback(void* clientp, double dltotal, double dlnow, double, double) {
        ProgressInfo* progress = static_cast<ProgressInfo*>(clientp);
        
        if (progress && progress->show_progress && dltotal > 0) {
            progress->total_size = static_cast<size_t>(dltotal);
            progress->downloaded_size = static_cast<size_t>(dlnow);
            
            double percentage = (dlnow / dltotal) * 100.0;
            
            // Calculate speed
            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - progress->start_time);
            double speed = 0.0;
            if (elapsed.count() > 0) {
                speed = dlnow / elapsed.count();
            }
            
            // Create progress bar
            int bar_width = 40;
            int filled = static_cast<int>(percentage / 100.0 * bar_width);
            
            std::cout << "\r[";
            for (int i = 0; i < bar_width; ++i) {
                if (i < filled) std::cout << "=";
                else if (i == filled) std::cout << ">";
                else std::cout << " ";
            }
            std::cout << "] " << std::fixed << std::setprecision(1) << percentage << "% "
                      << formatBytes(static_cast<size_t>(dlnow)) << "/"
                      << formatBytes(static_cast<size_t>(dltotal)) << " "
                      << formatSpeed(speed);
            std::cout.flush();
        }
        
        return 0;
    }

    static std::string formatBytes(size_t bytes) {
        const char* units[] = {"B", "KB", "MB", "GB", "TB"};
        const size_t num_units = sizeof(units) / sizeof(units[0]);
        
        double size = static_cast<double>(bytes);
        size_t unit_index = 0;
        
        while (size >= 1024.0 && unit_index < num_units - 1) {
            size /= 1024.0;
            ++unit_index;
        }
        
        std::ostringstream oss;
        oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
        return oss.str();
    }

    static std::string formatSpeed(double bytes_per_second) {
        return formatBytes(static_cast<size_t>(bytes_per_second)) + "/s";
    }

public:
    bool download(const std::string& url, const std::string& output_file, bool show_progress = true) {
        std::cout << "Starting download...\n";
        std::cout << "URL: " << url << "\n";
        std::cout << "Output: " << output_file << "\n\n";

#ifndef MOCK_CURL
        // Initialize libcurl
        curl_global_init(CURL_GLOBAL_DEFAULT);
        CURL* curl = curl_easy_init();
        
        if (!curl) {
            std::cerr << "Failed to initialize curl\n";
            return false;
        }

        // Open output file
        std::ofstream file(output_file, std::ios::binary);
        if (!file.is_open()) {
            std::cerr << "Failed to open output file: " << output_file << "\n";
            curl_easy_cleanup(curl);
            curl_global_cleanup();
            return false;
        }

        // Setup progress tracking
        ProgressInfo progress;
        progress.start_time = std::chrono::steady_clock::now();
        progress.show_progress = show_progress;

        // Configure curl
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &file);
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_USERAGENT, "SimpleDownloader/1.0");
        
        if (show_progress) {
            curl_easy_setopt(curl, CURLOPT_PROGRESSFUNCTION, ProgressCallback);
            curl_easy_setopt(curl, CURLOPT_PROGRESSDATA, &progress);
            curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
        }

        // Perform download
        CURLcode res = curl_easy_perform(curl);

        // Cleanup
        file.close();
        curl_easy_cleanup(curl);
        curl_global_cleanup();

        if (show_progress) {
            std::cout << "\n";
        }

        if (res == CURLE_OK) {
            std::cout << "Download completed successfully!\n";
            std::cout << "File saved to: " << output_file << "\n";
            return true;
        } else {
            std::cerr << "Download failed!\n";
            return false;
        }
#else
        // Mock implementation for demonstration
        std::cout << "Mock download simulation...\n";
        
        std::ofstream file(output_file);
        if (!file.is_open()) {
            std::cerr << "Failed to create output file\n";
            return false;
        }
        
        // Simulate download with progress
        const size_t total_size = 1024 * 1024; // 1MB
        const size_t chunk_size = 1024; // 1KB chunks
        
        for (size_t downloaded = 0; downloaded < total_size; downloaded += chunk_size) {
            // Write some data
            std::string data(chunk_size, 'X');
            file.write(data.c_str(), chunk_size);
            
            // Show progress
            if (show_progress) {
                double percentage = (static_cast<double>(downloaded) / total_size) * 100.0;
                int bar_width = 40;
                int filled = static_cast<int>(percentage / 100.0 * bar_width);
                
                std::cout << "\r[";
                for (int i = 0; i < bar_width; ++i) {
                    if (i < filled) std::cout << "=";
                    else if (i == filled) std::cout << ">";
                    else std::cout << " ";
                }
                std::cout << "] " << std::fixed << std::setprecision(1) << percentage << "% "
                          << formatBytes(downloaded) << "/" << formatBytes(total_size);
                std::cout.flush();
            }
            
            // Simulate network delay
#ifdef _WIN32
            Sleep(10);
#else
            usleep(10000);
#endif
        }
        
        file.close();
        
        if (show_progress) {
            std::cout << "\n";
        }
        
        std::cout << "Mock download completed!\n";
        std::cout << "File saved to: " << output_file << "\n";
        return true;
#endif
    }
};

void printUsage(const std::string& program_name) {
    std::cout << "Simple IDM Downloader\n\n";
    std::cout << "Usage: " << program_name << " [OPTIONS] URL [OUTPUT_FILE]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -h, --help     Show this help message\n";
    std::cout << "  -q, --quiet    Quiet mode (no progress display)\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << " https://example.com/file.zip\n";
    std::cout << "  " << program_name << " -q https://example.com/file.zip myfile.zip\n";
}

std::string extractFileName(const std::string& url) {
    size_t last_slash = url.find_last_of('/');
    if (last_slash != std::string::npos && last_slash < url.length() - 1) {
        std::string filename = url.substr(last_slash + 1);
        // Remove query parameters
        size_t query_pos = filename.find('?');
        if (query_pos != std::string::npos) {
            filename = filename.substr(0, query_pos);
        }
        return filename.empty() ? "downloaded_file" : filename;
    }
    return "downloaded_file";
}

int main(int argc, char* argv[]) {
    std::string url;
    std::string output_file;
    bool show_progress = true;
    bool show_help = false;

    // Parse command line arguments
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            show_help = true;
        } else if (arg == "-q" || arg == "--quiet") {
            show_progress = false;
        } else if (arg[0] != '-') {
            if (url.empty()) {
                url = arg;
            } else if (output_file.empty()) {
                output_file = arg;
            }
        }
    }

    if (show_help || url.empty()) {
        printUsage(argv[0]);
        return show_help ? 0 : 1;
    }

    // Generate output filename if not provided
    if (output_file.empty()) {
        output_file = extractFileName(url);
    }

    std::cout << "Simple IDM Downloader v1.0\n";
    std::cout << "===========================\n\n";

    SimpleDownloader downloader;
    bool success = downloader.download(url, output_file, show_progress);

    return success ? 0 : 1;
}
