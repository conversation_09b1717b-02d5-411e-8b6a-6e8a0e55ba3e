#!/bin/bash

# IDM Downloader Build Script
# This script builds the IDM Downloader project

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    print_error "CMakeLists.txt not found. Please run this script from the project root directory."
    exit 1
fi

# Parse command line arguments
BUILD_TYPE="Release"
CLEAN_BUILD=false
INSTALL=false
JOBS=$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -i|--install)
            INSTALL=true
            shift
            ;;
        -j|--jobs)
            JOBS="$2"
            shift 2
            ;;
        -h|--help)
            echo "IDM Downloader Build Script"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  -d, --debug     Build in debug mode (default: Release)"
            echo "  -c, --clean     Clean build directory before building"
            echo "  -i, --install   Install after building"
            echo "  -j, --jobs N    Number of parallel jobs (default: auto-detect)"
            echo "  -h, --help      Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                    # Build in release mode"
            echo "  $0 --debug           # Build in debug mode"
            echo "  $0 --clean --install # Clean build and install"
            echo "  $0 -j 8              # Build with 8 parallel jobs"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use -h or --help for usage information."
            exit 1
            ;;
    esac
done

print_status "Starting IDM Downloader build process..."
print_status "Build type: $BUILD_TYPE"
print_status "Parallel jobs: $JOBS"

# Check for required dependencies
print_status "Checking dependencies..."

# Check for CMake
if ! command -v cmake &> /dev/null; then
    print_error "CMake is not installed. Please install CMake 3.16 or higher."
    exit 1
fi

CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
print_status "Found CMake version: $CMAKE_VERSION"

# Check for libcurl
if ! pkg-config --exists libcurl; then
    print_warning "libcurl development headers not found via pkg-config."
    print_warning "Make sure libcurl development package is installed:"
    print_warning "  Ubuntu/Debian: sudo apt install libcurl4-openssl-dev"
    print_warning "  CentOS/RHEL: sudo yum install libcurl-devel"
    print_warning "  Fedora: sudo dnf install libcurl-devel"
    print_warning "  macOS: brew install curl"
fi

# Create build directory
BUILD_DIR="build"

if [ "$CLEAN_BUILD" = true ]; then
    print_status "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

if [ ! -d "$BUILD_DIR" ]; then
    print_status "Creating build directory..."
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# Configure with CMake
print_status "Configuring project with CMake..."
cmake -DCMAKE_BUILD_TYPE="$BUILD_TYPE" ..

if [ $? -ne 0 ]; then
    print_error "CMake configuration failed!"
    exit 1
fi

# Build the project
print_status "Building project..."
make -j"$JOBS"

if [ $? -ne 0 ]; then
    print_error "Build failed!"
    exit 1
fi

print_success "Build completed successfully!"

# Check if executable was created
EXECUTABLE="IDMDownloader"
if [ -f "$EXECUTABLE" ]; then
    print_success "Executable created: $BUILD_DIR/$EXECUTABLE"
    
    # Show file size and basic info
    FILE_SIZE=$(ls -lh "$EXECUTABLE" | awk '{print $5}')
    print_status "Executable size: $FILE_SIZE"
    
    # Test if executable runs
    if ./"$EXECUTABLE" --help > /dev/null 2>&1; then
        print_success "Executable test passed!"
    else
        print_warning "Executable test failed - there might be missing dependencies"
    fi
else
    print_error "Executable not found after build!"
    exit 1
fi

# Install if requested
if [ "$INSTALL" = true ]; then
    print_status "Installing..."
    sudo make install
    
    if [ $? -eq 0 ]; then
        print_success "Installation completed!"
    else
        print_error "Installation failed!"
        exit 1
    fi
fi

# Final instructions
echo ""
print_success "Build process completed!"
echo ""
print_status "To run the downloader:"
echo "  cd $BUILD_DIR && ./$EXECUTABLE --help"
echo ""
print_status "Example usage:"
echo "  ./$EXECUTABLE https://example.com/file.zip"
echo "  ./$EXECUTABLE -c 8 -o myfile.zip https://example.com/file.zip"
echo ""

if [ "$INSTALL" = true ]; then
    print_status "Since you installed the program, you can also run it from anywhere:"
    echo "  $EXECUTABLE https://example.com/file.zip"
fi
