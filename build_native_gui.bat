@echo off
echo Building IDM Downloader Native Windows GUI...

REM Build the native Windows GUI (no external dependencies)
echo Compiling native Windows GUI...

g++ -std=c++17 -I include ^
    native_gui/windows_gui.cpp ^
    src/downloader.cpp ^
    src/http_client.cpp ^
    src/file_manager.cpp ^
    src/progress_tracker.cpp ^
    src/utils.cpp ^
    -lcurl -lws2_32 -lwldap32 -lcomctl32 -lcomdlg32 -lshell32 ^
    -mwindows ^
    -o IDMDownloaderNativeGUI.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ Native GUI build successful!
    echo.
    echo 🖥️  To run the Native Windows GUI:
    echo   Run: IDMDownloaderNativeGUI.exe
    echo.
    echo ✨ Features:
    echo   - Native Windows look and feel
    echo   - No external dependencies
    echo   - Fast and lightweight
    echo   - Integrated with Windows
    echo.
) else (
    echo.
    echo ❌ Build failed!
    echo Make sure you have:
    echo   - GCC with C++17 support
    echo   - libcurl development libraries
    echo   - Windows development libraries
)

pause
