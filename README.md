# IDM Downloader - C++ HTTP/HTTPS Downloader

A modern, feature-rich C++ downloader inspired by Internet Download Manager (IDM). This downloader supports multi-threaded downloads, resume functionality, progress tracking, and more.

## Features

- **Multi-threaded Downloads**: Parallel connections for faster download speeds
- **Resume Support**: Continue interrupted downloads automatically
- **Progress Tracking**: Real-time progress display with speed and ETA
- **HTTP/HTTPS Support**: Full support for both protocols with SSL verification
- **Range Requests**: Efficient partial content downloads
- **Command Line Interface**: Easy-to-use CLI with various options
- **Cross-platform**: Works on Windows, Linux, and macOS

## Dependencies

- **libcurl**: For HTTP/HTTPS operations
- **CMake**: Build system (version 3.16 or higher)
- **C++17 compatible compiler**: GCC 7+, Clang 5+, or MSVC 2017+

### Installing Dependencies

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install build-essential cmake libcurl4-openssl-dev
```

#### CentOS/RHEL/Fedora
```bash
# CentOS/RHEL
sudo yum install gcc-c++ cmake libcurl-devel

# Fedora
sudo dnf install gcc-c++ cmake libcurl-devel
```

#### macOS
```bash
# Using Homebrew
brew install cmake curl

# Using MacPorts
sudo port install cmake curl
```

#### Windows
- Install Visual Studio 2017 or later with C++ support
- Install CMake from https://cmake.org/download/
- Install vcpkg and use it to install curl:
  ```cmd
  vcpkg install curl:x64-windows
  ```

## Building

### Linux/macOS
```bash
# Clone or navigate to the project directory
cd IDM

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake ..

# Build
make -j$(nproc)

# The executable will be created as 'IDMDownloader'
```

### Windows (Visual Studio)
```cmd
# Navigate to project directory
cd IDM

# Create build directory
mkdir build && cd build

# Configure with CMake (adjust path to vcpkg if needed)
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake

# Build
cmake --build . --config Release

# The executable will be created as 'IDMDownloader.exe'
```

## Usage

### Basic Usage
```bash
# Download a file
./IDMDownloader https://example.com/file.zip

# Download with custom output filename
./IDMDownloader -o myfile.zip https://example.com/file.zip

# Download with 8 parallel connections
./IDMDownloader -c 8 https://example.com/file.zip
```

### Command Line Options
```
Usage: IDMDownloader [OPTIONS] URL [OUTPUT_FILE]

Options:
  -h, --help              Show help message
  -o, --output FILE       Output file path
  -c, --connections N     Number of parallel connections (default: 4)
  -r, --resume            Resume incomplete download (default: true)
  --no-resume             Disable resume functionality
  -q, --quiet             Quiet mode (no progress display)
  -t, --timeout N         Timeout in seconds (default: 30)
  -u, --user-agent STR    Custom user agent string
  --chunk-size N          Chunk size in bytes (default: 1MB)
```

### Examples
```bash
# Basic download
./IDMDownloader https://releases.ubuntu.com/20.04/ubuntu-20.04.3-desktop-amd64.iso

# Download with custom settings
./IDMDownloader -o ubuntu.iso -c 8 -t 60 https://releases.ubuntu.com/20.04/ubuntu-20.04.3-desktop-amd64.iso

# Quiet download (no progress display)
./IDMDownloader -q https://example.com/largefile.zip

# Download without resume capability
./IDMDownloader --no-resume https://example.com/file.tar.gz

# Custom user agent
./IDMDownloader -u "MyDownloader/1.0" https://example.com/file.pdf
```

## Features in Detail

### Multi-threaded Downloads
The downloader automatically detects if the server supports range requests and splits large files into chunks for parallel downloading. This can significantly improve download speeds, especially for large files.

### Resume Functionality
If a download is interrupted, the downloader can automatically resume from where it left off. Resume information is stored in `.idm_resume` files alongside the target file.

### Progress Tracking
Real-time progress display includes:
- Progress bar
- Percentage completed
- Downloaded size / Total size
- Current download speed
- Estimated time of arrival (ETA)

### Error Handling
The downloader includes comprehensive error handling for:
- Network timeouts
- Connection failures
- File system errors
- Invalid URLs
- Server errors

## Architecture

The project is structured with the following main components:

- **Downloader**: Main orchestrator class that manages the download process
- **HttpClient**: HTTP/HTTPS client using libcurl for network operations
- **FileManager**: Handles file operations, chunking, and resume functionality
- **ProgressTracker**: Tracks and calculates download statistics
- **Utils**: Utility functions for string manipulation, file operations, etc.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is open source. Feel free to use, modify, and distribute according to your needs.

## Troubleshooting

### Common Issues

1. **libcurl not found**: Make sure libcurl development headers are installed
2. **Permission denied**: Check write permissions for the output directory
3. **SSL certificate errors**: Update your system's CA certificates
4. **Slow downloads**: Try increasing the number of connections with `-c`

### Debug Mode
To build with debug information:
```bash
cmake -DCMAKE_BUILD_TYPE=Debug ..
make
```

### Verbose Output
For debugging network issues, you can modify the source to enable libcurl verbose mode.

## Performance Tips

1. **Optimal connection count**: Usually 4-8 connections work best
2. **Chunk size**: Default 1MB works well for most cases
3. **Network conditions**: Adjust timeout based on your connection stability
4. **Server limitations**: Some servers may limit concurrent connections

## Future Enhancements

- GUI interface
- Download scheduling
- Bandwidth limiting
- Proxy support
- FTP support
- Download queues
- Browser integration
