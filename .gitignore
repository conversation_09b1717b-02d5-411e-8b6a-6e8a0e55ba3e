# IDM Downloader - Git Ignore File

# Build directories
build/
bin/
obj/
out/

# CMake generated files
CMakeCache.txt
CMakeFiles/
cmake_install.cmake
Makefile
*.cmake
!CMakeLists.txt

# Visual Studio / MSBuild
.vs/
*.vcxproj
*.vcxproj.filters
*.vcxproj.user
*.sln
*.suo
*.user
*.aps
*.pch
*.vspscc
*_i.c
*_p.c
*.ncb
*.tlb
*.tlh
*.bak
*.cache
*.ilk
*.log
*.lib
*.sbr
*.sdf
*.opensdf
*.unsuccessfulbuild
ipch/
*.idb
*.pgc
*.pgd
*.rsp
*.tmp
*.tmp_proj
*.vssscc
*.pidb
*.svclog
*.scc

# Executables
*.exe
*.out
*.app
IDMDownloader
IDMDownloader.exe

# Object files
*.o
*.obj
*.ko
*.elf

# Linker output
*.so
*.so.*
*.dylib
*.dll
*.a
*.la
*.lo

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Precompiled Headers
*.gch
*.pch

# Libraries
*.lib
*.a
*.la
*.lo

# Shared objects (inc. Windows DLLs)
*.dll
*.so
*.so.*
*.dylib

# Archives
*.tar.gz
*.tar.bz2
*.tar.xz
*.zip
*.7z
*.rar

# Package files
*.deb
*.rpm
*.pkg
*.dmg

# Test files and downloads
test_download*
*.idm_resume
downloaded_*
temp_chunk_*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.old

# Log files
*.log

# Core dumps
core
core.*

# Valgrind output
*.valgrind

# Coverage files
*.gcov
*.gcda
*.gcno
coverage/
*.coverage

# Profiling files
*.prof
gmon.out

# Documentation generated files
docs/html/
docs/latex/
docs/man/
*.pdf
*.ps

# Package manager files
vcpkg_installed/
packages/
node_modules/

# Environment files
.env
.env.local
.env.*.local

# Editor backup files
*~
*.orig
*.rej

# Patch files
*.patch
*.diff

# Configuration files (if they contain sensitive data)
config.ini
settings.conf

# Custom ignore patterns
# Add your own patterns here
