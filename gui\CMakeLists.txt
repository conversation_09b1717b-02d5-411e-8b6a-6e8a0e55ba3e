cmake_minimum_required(VERSION 3.16)

project(IDMDownloaderGUI VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable Qt MOC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Find libcurl
find_package(PkgConfig REQUIRED)
pkg_check_modules(CURL REQUIRED libcurl)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../include)
include_directories(${CURL_INCLUDE_DIRS})

# Source files
set(GUI_SOURCES
    main.cpp
    main_window.cpp
    main_window_slots.cpp
    download_worker.cpp
    ../src/downloader.cpp
    ../src/http_client.cpp
    ../src/file_manager.cpp
    ../src/progress_tracker.cpp
    ../src/utils.cpp
)

# Header files
set(GUI_HEADERS
    main_window.h
    ../include/downloader.h
    ../include/http_client.h
    ../include/file_manager.h
    ../include/progress_tracker.h
    ../include/utils.h
)

# Create executable
add_executable(IDMDownloaderGUI ${GUI_SOURCES} ${GUI_HEADERS})

# Link libraries
target_link_libraries(IDMDownloaderGUI 
    Qt6::Core 
    Qt6::Widgets
    ${CURL_LIBRARIES}
)

# Compiler flags
target_compile_options(IDMDownloaderGUI PRIVATE ${CURL_CFLAGS_OTHER})

# Windows specific settings
if(WIN32)
    set_target_properties(IDMDownloaderGUI PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
    
    # Add Windows libraries
    target_link_libraries(IDMDownloaderGUI ws2_32 wldap32)
endif()

# Install target
install(TARGETS IDMDownloaderGUI
    RUNTIME DESTINATION bin
)
