/*
 * Simple Web-based GUI for IDM Downloader
 * This creates a local web server that provides a browser-based interface
 * No additional GUI frameworks required!
 */

#include "../include/downloader.h"
#include <iostream>
#include <string>
#include <thread>
#include <sstream>
#include <fstream>
#include <map>
#include <regex>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#endif

class WebGUIServer {
private:
    int port_;
    bool running_;
    std::unique_ptr<Downloader> downloader_;
    DownloadStats current_stats_;
    std::string current_status_;
    
public:
    WebGUIServer(int port = 8080) : port_(port), running_(false) {
        downloader_ = std::make_unique<Downloader>();
        current_status_ = "Ready";
    }
    
    void start() {
        running_ = true;
        
#ifdef _WIN32
        WSADATA wsaData;
        WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
        
        int server_fd = socket(AF_INET, SOCK_STREAM, 0);
        if (server_fd < 0) {
            std::cerr << "Failed to create socket" << std::endl;
            return;
        }
        
        int opt = 1;
        setsockopt(server_fd, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
        
        sockaddr_in address{};
        address.sin_family = AF_INET;
        address.sin_addr.s_addr = INADDR_ANY;
        address.sin_port = htons(port_);
        
        if (bind(server_fd, (sockaddr*)&address, sizeof(address)) < 0) {
            std::cerr << "Failed to bind to port " << port_ << std::endl;
            return;
        }
        
        if (listen(server_fd, 3) < 0) {
            std::cerr << "Failed to listen on socket" << std::endl;
            return;
        }
        
        std::cout << "🌐 IDM Downloader Web GUI started!" << std::endl;
        std::cout << "📱 Open your browser and go to: http://localhost:" << port_ << std::endl;
        std::cout << "🛑 Press Ctrl+C to stop the server" << std::endl << std::endl;
        
        while (running_) {
            sockaddr_in client_addr{};
            socklen_t client_len = sizeof(client_addr);
            
            int client_fd = accept(server_fd, (sockaddr*)&client_addr, &client_len);
            if (client_fd < 0) continue;
            
            std::thread([this, client_fd]() {
                handleRequest(client_fd);
#ifdef _WIN32
                closesocket(client_fd);
#else
                close(client_fd);
#endif
            }).detach();
        }
        
#ifdef _WIN32
        closesocket(server_fd);
        WSACleanup();
#else
        close(server_fd);
#endif
    }
    
    void stop() {
        running_ = false;
    }
    
private:
    void handleRequest(int client_fd) {
        char buffer[4096] = {0};
        recv(client_fd, buffer, sizeof(buffer), 0);
        
        std::string request(buffer);
        std::string response;
        
        if (request.find("GET / ") != std::string::npos) {
            response = getMainPage();
        } else if (request.find("GET /api/status") != std::string::npos) {
            response = getStatusAPI();
        } else if (request.find("POST /api/download") != std::string::npos) {
            response = handleDownloadAPI(request);
        } else if (request.find("GET /style.css") != std::string::npos) {
            response = getStyleSheet();
        } else {
            response = get404Page();
        }
        
        send(client_fd, response.c_str(), response.length(), 0);
    }
    
    std::string getMainPage() {
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: text/html\r\n"
               "Connection: close\r\n\r\n"
               R"(<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IDM Downloader - Web Interface</title>
    <link rel="stylesheet" href="/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 IDM Downloader</h1>
            <p>Professional Multi-threaded Download Manager</p>
        </header>
        
        <main>
            <div class="download-section">
                <h2>📥 New Download</h2>
                <form id="downloadForm">
                    <div class="form-group">
                        <label for="url">Download URL:</label>
                        <input type="url" id="url" name="url" placeholder="https://example.com/file.zip" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="filename">Save as:</label>
                        <input type="text" id="filename" name="filename" placeholder="filename.zip">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="connections">Connections:</label>
                            <select id="connections" name="connections">
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="4" selected>4</option>
                                <option value="8">8</option>
                                <option value="16">16</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="resume" name="resume" checked>
                                Resume download
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-primary">Start Download</button>
                </form>
            </div>
            
            <div class="progress-section">
                <h2>📊 Download Progress</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                        <span class="progress-text" id="progressText">0%</span>
                    </div>
                </div>
                
                <div class="stats">
                    <div class="stat">
                        <span class="label">Status:</span>
                        <span id="status">Ready</span>
                    </div>
                    <div class="stat">
                        <span class="label">Speed:</span>
                        <span id="speed">0 KB/s</span>
                    </div>
                    <div class="stat">
                        <span class="label">Downloaded:</span>
                        <span id="downloaded">0 B</span>
                    </div>
                    <div class="stat">
                        <span class="label">Total Size:</span>
                        <span id="totalSize">Unknown</span>
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <p>IDM Downloader v1.0 - Built with C++ ❤️</p>
        </footer>
    </div>
    
    <script>
        // Auto-refresh status every second
        setInterval(updateStatus, 1000);
        
        function updateStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status').textContent = data.status;
                    document.getElementById('speed').textContent = data.speed;
                    document.getElementById('downloaded').textContent = data.downloaded;
                    document.getElementById('totalSize').textContent = data.totalSize;
                    document.getElementById('progressText').textContent = data.progress + '%';
                    document.getElementById('progressFill').style.width = data.progress + '%';
                });
        }
        
        document.getElementById('downloadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('/api/download', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Download started!');
                } else {
                    alert('Error: ' + data.error);
                }
            });
        });
    </script>
</body>
</html>)";
    }
    
    std::string getStyleSheet() {
        return "HTTP/1.1 200 OK\r\n"
               "Content-Type: text/css\r\n"
               "Connection: close\r\n\r\n"
               R"(
* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    color: white;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.download-section, .progress-section {
    margin-bottom: 30px;
}

h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 20px;
    align-items: end;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #4a5568;
}

input[type="url"], input[type="text"], select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s;
}

input[type="url"]:focus, input[type="text"]:focus, select:focus {
    outline: none;
    border-color: #667eea;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.progress-container {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background: #e2e8f0;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #48bb78, #38a169);
    border-radius: 15px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 600;
    color: #4a5568;
}

.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat {
    background: #f7fafc;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.label {
    font-weight: 600;
    color: #4a5568;
}

footer {
    text-align: center;
    color: white;
    margin-top: 20px;
    opacity: 0.8;
}
)";
    }
    
    std::string getStatusAPI() {
        std::ostringstream json;
        json << "HTTP/1.1 200 OK\r\n"
             << "Content-Type: application/json\r\n"
             << "Connection: close\r\n\r\n"
             << "{"
             << "\"status\":\"" << current_status_ << "\","
             << "\"progress\":" << (current_stats_.total_size > 0 ? 
                (current_stats_.downloaded_size * 100 / current_stats_.total_size) : 0) << ","
             << "\"speed\":\"" << formatBytes(current_stats_.download_speed) << "/s\","
             << "\"downloaded\":\"" << formatBytes(current_stats_.downloaded_size) << "\","
             << "\"totalSize\":\"" << formatBytes(current_stats_.total_size) << "\""
             << "}";
        return json.str();
    }
    
    std::string handleDownloadAPI(const std::string& request) {
        // Extract JSON from POST body (simplified)
        size_t json_start = request.find("\r\n\r\n");
        if (json_start == std::string::npos) {
            return "HTTP/1.1 400 Bad Request\r\n\r\n{\"success\":false,\"error\":\"Invalid request\"}";
        }
        
        std::string json_body = request.substr(json_start + 4);
        
        // Simple JSON parsing (for demo - in production use a proper JSON library)
        std::regex url_regex(R"("url"\s*:\s*"([^"]+)")");
        std::smatch url_match;
        
        if (!std::regex_search(json_body, url_match, url_regex)) {
            return "HTTP/1.1 400 Bad Request\r\n\r\n{\"success\":false,\"error\":\"URL required\"}";
        }
        
        std::string url = url_match[1].str();
        current_status_ = "Starting download...";
        
        // Start download in background thread
        std::thread([this, url]() {
            DownloadConfig config;
            config.url = url;
            config.output_path = "downloaded_file";
            config.num_connections = 4;
            
            auto progress_cb = [this](const DownloadStats& stats) {
                current_stats_ = stats;
                current_status_ = "Downloading...";
            };
            
            auto completion_cb = [this](bool success, const std::string& error) {
                if (success) {
                    current_status_ = "Download completed!";
                } else {
                    current_status_ = "Download failed: " + error;
                }
            };
            
            downloader_->downloadAsync(config, progress_cb, completion_cb);
        }).detach();
        
        return "HTTP/1.1 200 OK\r\n\r\n{\"success\":true}";
    }
    
    std::string get404Page() {
        return "HTTP/1.1 404 Not Found\r\n\r\n<h1>404 - Page Not Found</h1>";
    }
    
    std::string formatBytes(size_t bytes) {
        if (bytes < 1024) return std::to_string(bytes) + " B";
        if (bytes < 1024 * 1024) return std::to_string(bytes / 1024) + " KB";
        if (bytes < 1024 * 1024 * 1024) return std::to_string(bytes / (1024 * 1024)) + " MB";
        return std::to_string(bytes / (1024 * 1024 * 1024)) + " GB";
    }
};

int main() {
    std::cout << "🌐 Starting IDM Downloader Web GUI..." << std::endl;
    
    WebGUIServer server(8080);
    server.start();
    
    return 0;
}
