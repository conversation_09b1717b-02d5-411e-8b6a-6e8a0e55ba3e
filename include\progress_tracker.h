#pragma once

#include <chrono>
#include <string>
#include <atomic>
#include <mutex>
#include <deque>

class ProgressTracker {
public:
    ProgressTracker();
    ~ProgressTracker();

    // Update progress
    void updateProgress(size_t downloaded, size_t total);
    void setTotalSize(size_t total);
    void addDownloaded(size_t bytes);
    
    // Get statistics
    double getProgressPercentage() const;
    double getDownloadSpeed() const; // bytes per second
    std::string getETA() const;
    size_t getTotalSize() const;
    size_t getDownloadedSize() const;
    
    // Control
    void start();
    void pause();
    void resume();
    void reset();
    
    // Display
    std::string getProgressBar(int width = 50) const;
    std::string getFormattedStats() const;
    void printProgress() const;

private:
    mutable std::mutex mutex_;
    
    std::atomic<size_t> total_size_{0};
    std::atomic<size_t> downloaded_size_{0};
    
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point last_update_time_;
    std::atomic<bool> is_active_{false};
    std::atomic<bool> is_paused_{false};
    
    // Speed calculation
    struct SpeedSample {
        std::chrono::steady_clock::time_point timestamp;
        size_t bytes;
    };
    
    std::deque<SpeedSample> speed_samples_;
    static constexpr size_t MAX_SPEED_SAMPLES = 10;
    static constexpr std::chrono::seconds SPEED_WINDOW{5};
    
    // Helper functions
    void updateSpeedSamples();
    double calculateSpeed() const;
    std::string formatBytes(size_t bytes) const;
    std::string formatTime(std::chrono::seconds seconds) const;
};
